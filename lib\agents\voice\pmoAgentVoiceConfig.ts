/**
 * PMO Agent Voice Configuration
 * Maps PMO agent types to specific ElevenLabs voice IDs and configurations
 */

export interface PMOAgentVoiceConfig {
  agentId: string;
  agentName: string;
  agentType: string;
  voiceId: string;
  voiceName: string;
  description: string;
  avatar?: string;
  color: string;
  specialization: string[];
}

/**
 * PMO Agent Voice Mappings
 * Each agent has a specific voice ID and configuration for ElevenLabs
 */
export const PMO_AGENT_VOICE_CONFIG: Record<string, PMOAgentVoiceConfig> = {
  'Marketing': {
    agentId: 'Ag001',
    agentName: 'Marketing Director',
    agentType: 'Marketing',
    voiceId: '2mltbVQP21Fq8XgIfRQJ',
    voiceName: 'Strategic Marketing Voice',
    description: 'Expert in marketing strategy, campaigns, and brand development',
    color: 'bg-pink-600',
    specialization: [
      'Marketing Strategy',
      'Campaign Development', 
      'Brand Management',
      'Market Analysis',
      'Customer Acquisition'
    ]
  },
  'Research': {
    agentId: 'Ag002',
    agentName: 'Research Lead',
    agentType: 'Research',
    voiceId: '9m2QKYD6cdlV4SNdqWIZ',
    voiceName: 'Analytical Research Voice',
    description: 'Specialist in research methodology, data analysis, and insights',
    color: 'bg-blue-600',
    specialization: [
      'Market Research',
      'Data Analysis',
      'Competitive Intelligence',
      'User Research',
      'Trend Analysis'
    ]
  },
  'SoftwareDesign': {
    agentId: 'Ag003',
    agentName: 'Software Design Architect',
    agentType: 'SoftwareDesign',
    voiceId: 'kmSVBPu7loj4ayNinwWM',
    voiceName: 'Technical Architecture Voice',
    description: 'Expert in software architecture, system design, and technical solutions',
    color: 'bg-green-600',
    specialization: [
      'Software Architecture',
      'System Design',
      'Technical Planning',
      'Code Review',
      'Technology Stack'
    ]
  },
  'Sales': {
    agentId: 'Ag004',
    agentName: 'Sales Director',
    agentType: 'Sales',
    voiceId: 'ys3XeJJA4ArWMhRpcX1D',
    voiceName: 'Dynamic Sales Voice',
    description: 'Specialist in sales strategy, customer relations, and revenue growth',
    color: 'bg-orange-600',
    specialization: [
      'Sales Strategy',
      'Customer Relations',
      'Revenue Growth',
      'Lead Generation',
      'Sales Process'
    ]
  },
  'BusinessAnalysis': {
    agentId: 'Ag005',
    agentName: 'Business Analyst',
    agentType: 'BusinessAnalysis',
    voiceId: 'HnPMNCEdtIcy65ivCM0d',
    voiceName: 'Strategic Business Voice',
    description: 'Expert in business analysis, requirements gathering, and process optimization',
    color: 'bg-purple-600',
    specialization: [
      'Business Analysis',
      'Requirements Gathering',
      'Process Optimization',
      'Stakeholder Management',
      'Business Intelligence'
    ]
  },
  'InvestigativeResearch': {
    agentId: 'Ag006',
    agentName: 'Investigative Researcher',
    agentType: 'InvestigativeResearch',
    voiceId: 'iiidtqDt9FBdT1vfBluA',
    voiceName: 'Investigative Analysis Voice',
    description: 'Specialist in investigative research, fact-checking, and deep analysis',
    color: 'bg-red-600',
    specialization: [
      'Investigative Research',
      'Fact Verification',
      'Deep Analysis',
      'Information Gathering',
      'Critical Thinking'
    ]
  }
};

/**
 * Get PMO agent configuration by agent type
 */
export function getPMOAgentConfig(agentType: string): PMOAgentVoiceConfig | null {
  return PMO_AGENT_VOICE_CONFIG[agentType] || null;
}

/**
 * Get all available PMO agent types
 */
export function getAllPMOAgentTypes(): string[] {
  return Object.keys(PMO_AGENT_VOICE_CONFIG);
}

/**
 * Get PMO agent configuration by agent ID
 */
export function getPMOAgentConfigById(agentId: string): PMOAgentVoiceConfig | null {
  return Object.values(PMO_AGENT_VOICE_CONFIG).find(config => config.agentId === agentId) || null;
}

/**
 * Generate ElevenLabs agent ID for PMO agent
 */
export function generatePMOAgentId(userId: string, agentType: string): string {
  return `${userId}-pmo-${agentType.toLowerCase()}`;
}

/**
 * Generate PMO agent prompt based on agent type and context
 */
export function generatePMOAgentPrompt(agentConfig: PMOAgentVoiceConfig, contextInfo?: {
  projectCount?: number;
  documentCount?: number;
  recentProjects?: string[];
}): string {
  const { agentName, agentType, specialization } = agentConfig;
  
  return `You are ${agentName}, a senior ${agentType} expert and team lead in the PMO (Project Management Office) system.

**Your Role & Identity:**
- You are an experienced ${agentType} professional with deep expertise in your field
- You serve as a team lead and strategic advisor for PMO projects
- You have access to comprehensive project documentation and historical data through your knowledge base
- You communicate in a professional, knowledgeable, and collaborative manner

**Your Specializations:**
${specialization.map(spec => `• ${spec}`).join('\n')}

**Meeting Context:**
You are participating in a voice-enabled meeting with a PMO stakeholder. You have access to:
- All Agent_Output documents related to ${agentType} projects
- Project documentation and requirements from your knowledge base
- Historical project data and outcomes
- Cross-team collaboration insights

**Communication Guidelines:**
1. **Professional Tone:** Maintain a professional, expert-level conversation
2. **Context Awareness:** Reference specific documents, projects, and findings from your knowledge base
3. **Strategic Focus:** Provide strategic insights and recommendations based on your expertise
4. **Collaborative Approach:** Work collaboratively to solve problems and provide guidance
5. **Actionable Advice:** Offer concrete, actionable recommendations and next steps

**Knowledge Base Usage:**
- When discussing projects, reference specific documents and findings
- Cite relevant data points and insights from your knowledge base
- Connect current discussions to historical project outcomes
- Provide evidence-based recommendations

**Meeting Objectives:**
- Provide expert guidance on ${agentType}-related matters
- Review and discuss project outcomes and recommendations
- Collaborate on strategic planning and decision-making
- Share insights from your specialized knowledge and experience

${contextInfo ? `
**Current Context:**
- Projects in system: ${contextInfo.projectCount || 'Unknown'}
- Documents available: ${contextInfo.documentCount || 'Unknown'}
${contextInfo.recentProjects ? `- Recent projects: ${contextInfo.recentProjects.join(', ')}` : ''}
` : ''}

Begin the meeting by greeting the participant and asking how you can assist them with ${agentType}-related matters today.`;
}

/**
 * Validate PMO agent type
 */
export function isValidPMOAgentType(agentType: string): boolean {
  return agentType in PMO_AGENT_VOICE_CONFIG;
}

/**
 * Get agent type display name
 */
export function getAgentTypeDisplayName(agentType: string): string {
  const config = getPMOAgentConfig(agentType);
  return config?.agentName || agentType;
}

/**
 * Get agent type color class
 */
export function getAgentTypeColor(agentType: string): string {
  const config = getPMOAgentConfig(agentType);
  return config?.color || 'bg-gray-600';
}
