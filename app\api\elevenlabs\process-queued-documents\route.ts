import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]/authOptions';
import { 
  uploadToKnowledgeBaseWithDeduplication, 
  computeRagIndex, 
  isDocumentIndexedForAgent 
} from '../../../../components/scriptreaderAI/uploadKnowledgebase';
import { getUserAgent, updateUserAgent } from '../../../../lib/firebase/userAgents';
import { updateAgentKnowledgeBase } from '../../../../components/scriptreaderAI/elevenlabs';
import { db } from '../../../../lib/firebase/config';
import { doc, getDoc } from 'firebase/firestore';

interface QueuedDocument {
  id: string;
  fileName: string;
  fileUrl: string;
  fileType: string;
  title?: string;
  content?: string;
}

interface ProcessQueuedDocumentsRequest {
  queuedDocumentIds: string[];
  agentType: string;
  forceUpload?: boolean;
  forceReindex?: boolean;
}

interface DocumentProcessingResult {
  documentId: string;
  fileName: string;
  success: boolean;
  wasExisting?: boolean;
  ragIndexed?: boolean;
  agentUpdated?: boolean;
  error?: string;
}

/**
 * API endpoint for processing queued documents when the user starts a conversation
 * 
 * This endpoint implements the deferred upload workflow:
 * 1. Check if user has an agent (create if needed)
 * 2. Process each queued document:
 *    - Upload to ElevenLabs knowledge base with deduplication
 *    - Index for RAG
 *    - Associate with agent
 * 3. Return processing results for all documents
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.email;

    // Parse request body
    const requestBody = await request.json();
    console.log(`[PROCESS_QUEUED_DOCS] Received request:`, JSON.stringify(requestBody, null, 2));

    const {
      queuedDocumentIds,
      agentType,
      forceUpload = false,
      forceReindex = false
    }: ProcessQueuedDocumentsRequest = requestBody;

    // Validate required parameters
    if (!queuedDocumentIds || !Array.isArray(queuedDocumentIds) || queuedDocumentIds.length === 0) {
      return NextResponse.json({
        error: 'Missing or empty queuedDocumentIds array'
      }, { status: 400 });
    }

    if (!agentType) {
      return NextResponse.json({
        error: 'Missing required parameter: agentType'
      }, { status: 400 });
    }

    // Get ElevenLabs API key
    const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY;
    if (!apiKey) {
      return NextResponse.json({
        error: 'ElevenLabs API key not configured'
      }, { status: 500 });
    }

    console.log(`[PROCESS_QUEUED_DOCS] Processing ${queuedDocumentIds.length} queued documents for agent type: ${agentType}`);

    // Step 1: Get or create user agent
    let userAgent;
    try {
      userAgent = await getUserAgent(userId, agentType);
      console.log(`[PROCESS_QUEUED_DOCS] Found user agent: ${userAgent.agentId}`);
    } catch (error) {
      console.error(`[PROCESS_QUEUED_DOCS] Error getting user agent:`, error);
      return NextResponse.json({
        error: 'Failed to get or create user agent'
      }, { status: 500 });
    }

    // Step 2: Fetch document metadata from Firestore
    const queuedDocuments: QueuedDocument[] = [];
    for (const documentId of queuedDocumentIds) {
      try {
        const docRef = doc(db, `users/${userId}/files`, documentId);
        const docSnap = await getDoc(docRef);
        
        if (docSnap.exists()) {
          const docData = docSnap.data();
          queuedDocuments.push({
            id: documentId,
            fileName: docData.name || `document_${documentId}`,
            fileUrl: docData.downloadUrl,
            fileType: docData.type || 'application/octet-stream',
            title: docData.title,
            content: docData.content
          });
        } else {
          console.warn(`[PROCESS_QUEUED_DOCS] Document not found: ${documentId}`);
        }
      } catch (error) {
        console.error(`[PROCESS_QUEUED_DOCS] Error fetching document ${documentId}:`, error);
      }
    }

    if (queuedDocuments.length === 0) {
      return NextResponse.json({
        error: 'No valid documents found in queue'
      }, { status: 400 });
    }

    console.log(`[PROCESS_QUEUED_DOCS] Found ${queuedDocuments.length} valid documents to process`);

    // Step 3: Process each document
    const results: DocumentProcessingResult[] = [];
    let successCount = 0;
    let errorCount = 0;

    for (const queuedDoc of queuedDocuments) {
      console.log(`[PROCESS_QUEUED_DOCS] Processing document: ${queuedDoc.fileName}`);
      
      try {
        // Check if document is already indexed for this agent
        const isAlreadyIndexed = await isDocumentIndexedForAgent(userAgent.agentId, queuedDoc.fileName, apiKey);
        if (isAlreadyIndexed && !forceUpload) {
          console.log(`[PROCESS_QUEUED_DOCS] Document ${queuedDoc.fileName} already indexed for agent ${userAgent.agentId}`);
          results.push({
            documentId: queuedDoc.id,
            fileName: queuedDoc.fileName,
            success: true,
            wasExisting: true,
            ragIndexed: true,
            agentUpdated: false
          });
          successCount++;
          continue;
        }

        // Upload document with deduplication
        console.log(`[PROCESS_QUEUED_DOCS] Uploading document: ${queuedDoc.fileName}`);
        const uploadResult = await uploadToKnowledgeBaseWithDeduplication(
          queuedDoc.fileUrl,
          queuedDoc.fileName,
          queuedDoc.fileType,
          apiKey,
          forceUpload
        );

        console.log(`[PROCESS_QUEUED_DOCS] Upload result for ${queuedDoc.fileName}:`, uploadResult);

        // Trigger RAG indexing
        console.log(`[PROCESS_QUEUED_DOCS] Starting RAG indexing for document: ${uploadResult.id}`);
        const ragResult = await computeRagIndex(
          uploadResult.id,
          apiKey,
          forceReindex
        );

        console.log(`[PROCESS_QUEUED_DOCS] RAG indexing result for ${queuedDoc.fileName}:`, ragResult);

        // Associate document with agent
        console.log(`[PROCESS_QUEUED_DOCS] Associating document with agent: ${userAgent.agentId}`);
        let agentUpdateResult;
        try {
          agentUpdateResult = await updateAgentKnowledgeBase(
            userAgent.agentId,
            uploadResult.id,
            apiKey
          );
          console.log(`[PROCESS_QUEUED_DOCS] Agent update result for ${queuedDoc.fileName}:`, agentUpdateResult);
        } catch (agentUpdateError) {
          console.error(`[PROCESS_QUEUED_DOCS] Error updating agent for ${queuedDoc.fileName}:`, agentUpdateError);
          // Continue anyway - document was uploaded and indexed
          agentUpdateResult = { success: false, error: agentUpdateError };
        }

        results.push({
          documentId: queuedDoc.id,
          fileName: queuedDoc.fileName,
          success: true,
          wasExisting: false,
          ragIndexed: ragResult.success || false,
          agentUpdated: agentUpdateResult.success || false
        });
        successCount++;

      } catch (error) {
        console.error(`[PROCESS_QUEUED_DOCS] Error processing document ${queuedDoc.fileName}:`, error);
        results.push({
          documentId: queuedDoc.id,
          fileName: queuedDoc.fileName,
          success: false,
          error: error instanceof Error ? error.message : String(error)
        });
        errorCount++;
      }
    }

    // Step 4: Update user agent metadata
    try {
      const documentsUploaded = (userAgent.metadata?.documentsUploaded || 0) + successCount;
      await updateUserAgent(userId, agentType, {
        metadata: {
          ...userAgent.metadata,
          documentsUploaded,
          lastKnowledgeBaseUpdate: new Date().toISOString(),
          lastQueueProcessing: {
            timestamp: new Date().toISOString(),
            documentsProcessed: queuedDocuments.length,
            successCount,
            errorCount
          }
        }
      });
      console.log(`[PROCESS_QUEUED_DOCS] Updated user agent metadata`);
    } catch (metadataError) {
      console.error(`[PROCESS_QUEUED_DOCS] Error updating user agent metadata:`, metadataError);
    }

    // Return results
    return NextResponse.json({
      success: true,
      message: `Processed ${queuedDocuments.length} documents: ${successCount} successful, ${errorCount} failed`,
      agentId: userAgent.agentId,
      results,
      summary: {
        totalDocuments: queuedDocuments.length,
        successCount,
        errorCount,
        documentsUploaded: (userAgent.metadata?.documentsUploaded || 0) + successCount
      }
    });

  } catch (error) {
    console.error('[PROCESS_QUEUED_DOCS] Unexpected error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
