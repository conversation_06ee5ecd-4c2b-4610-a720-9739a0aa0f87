"use client";

import React, { useState, useMemo } from 'react';
import { But<PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { 
  FileText, 
  Search, 
  Clock, 
  Tag, 
  CheckCircle2, 
  Circle,
  Filter,
  Download,
  Eye,
  Loader2
} from 'lucide-react';
import { DocumentContext } from './AgentMeetingRoom';

interface DocumentContextPanelProps {
  documentContext: DocumentContext;
  agentType: string | null;
  onDocumentSelect: (documentIds: string[]) => void;
}

interface AgentOutput {
  id: string;
  title: string;
  content: string;
  agentType: string;
  createdAt: string;
  updatedAt?: string;
  metadata?: Record<string, any>;
  category?: string;
  fileUrl?: string;
}

export default function DocumentContextPanel({
  documentContext,
  agentType,
  onDocumentSelect
}: DocumentContextPanelProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'recent' | 'with-files'>('all');
  const [expandedDocument, setExpandedDocument] = useState<string | null>(null);

  // Filter and search documents
  const filteredDocuments = useMemo(() => {
    let filtered = documentContext.documents;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter((doc: AgentOutput) =>
        doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        doc.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        doc.metadata?.category?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply type filter
    switch (selectedFilter) {
      case 'recent':
        filtered = filtered.filter((doc: AgentOutput) => {
          const createdAt = new Date(doc.createdAt);
          const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
          return createdAt > weekAgo;
        });
        break;
      case 'with-files':
        filtered = filtered.filter((doc: AgentOutput) => doc.fileUrl);
        break;
    }

    // Sort by creation date (newest first)
    return filtered.sort((a: AgentOutput, b: AgentOutput) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  }, [documentContext.documents, searchTerm, selectedFilter]);

  /**
   * Format timestamp for display
   */
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  /**
   * Toggle document selection
   */
  const toggleDocumentSelection = (documentId: string) => {
    const isSelected = documentContext.selectedDocuments.includes(documentId);
    const newSelection = isSelected
      ? documentContext.selectedDocuments.filter(id => id !== documentId)
      : [...documentContext.selectedDocuments, documentId];
    
    onDocumentSelect(newSelection);
  };

  /**
   * Select all filtered documents
   */
  const selectAllDocuments = () => {
    const allIds = filteredDocuments.map((doc: AgentOutput) => doc.id);
    onDocumentSelect(allIds);
  };

  /**
   * Clear all selections
   */
  const clearAllSelections = () => {
    onDocumentSelect([]);
  };

  /**
   * Toggle document expansion
   */
  const toggleDocumentExpansion = (documentId: string) => {
    setExpandedDocument(expandedDocument === documentId ? null : documentId);
  };

  return (
    <div className="h-full flex flex-col bg-gray-800 overflow-hidden">
      {/* Header */}
      <div className="p-4 border-b border-gray-700 flex-shrink-0">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-white flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Document Context
          </h3>
          {agentType && (
            <span className="text-xs text-gray-400 bg-gray-700 px-2 py-1 rounded-full">
              {agentType}
            </span>
          )}
        </div>

        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search documents..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          />
        </div>

        {/* Filters */}
        <div className="flex items-center space-x-2 mb-3">
          <Filter className="h-4 w-4 text-gray-400" />
          <select
            value={selectedFilter}
            onChange={(e) => setSelectedFilter(e.target.value as any)}
            className="bg-gray-700 border-gray-600 text-white text-sm rounded-md px-2 py-1 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            <option value="all">All Documents</option>
            <option value="recent">Recent (7 days)</option>
            <option value="with-files">With Files</option>
          </select>
        </div>

        {/* Selection Controls */}
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-400 truncate">
            {documentContext.selectedDocuments.length} of {filteredDocuments.length} selected
          </span>
          <div className="flex space-x-2 flex-shrink-0">
            <Button
              variant="ghost"
              size="sm"
              onClick={selectAllDocuments}
              className="text-xs text-gray-400 hover:text-white px-2 py-1"
            >
              Select All
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllSelections}
              className="text-xs text-gray-400 hover:text-white px-2 py-1"
            >
              Clear
            </Button>
          </div>
        </div>
      </div>

      {/* Document List - Scrollable Container */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full overflow-y-auto custom-scrollbar">
          {documentContext.isLoading ? (
            <div className="flex items-center justify-center h-32">
              <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
              <span className="ml-2 text-gray-400">Loading documents...</span>
            </div>
          ) : filteredDocuments.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-32 text-gray-400 p-4">
              <FileText className="h-8 w-8 mb-2" />
              <p className="text-sm text-center">No documents found</p>
              {searchTerm && (
                <p className="text-xs mt-1 text-center">Try adjusting your search terms</p>
              )}
            </div>
          ) : (
            <div className="space-y-3 p-4">
              {filteredDocuments.map((document: AgentOutput) => {
                const isSelected = documentContext.selectedDocuments.includes(document.id);
                const isExpanded = expandedDocument === document.id;

                return (
                  <div
                    key={document.id}
                    className={`
                      border rounded-lg p-3 cursor-pointer transition-all duration-200
                      ${isSelected
                        ? 'border-purple-500 bg-purple-500/10 shadow-lg'
                        : 'border-gray-600 bg-gray-700/50 hover:border-gray-500 hover:bg-gray-700/70'
                      }
                    `}
                  >
                    {/* Document Header */}
                    <div
                      className="flex items-start justify-between"
                      onClick={() => toggleDocumentSelection(document.id)}
                    >
                      <div className="flex items-start space-x-3 flex-1 min-w-0">
                        {/* Selection Checkbox */}
                        <div className="mt-1 flex-shrink-0">
                          {isSelected ? (
                            <CheckCircle2 className="h-4 w-4 text-purple-400" />
                          ) : (
                            <Circle className="h-4 w-4 text-gray-400" />
                          )}
                        </div>

                        {/* Document Info */}
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium text-white leading-tight mb-1">
                            <span className="line-clamp-2 break-words">
                              {document.title}
                            </span>
                          </h4>

                          <div className="flex items-center space-x-2 mt-1 flex-wrap">
                            <div className="flex items-center space-x-1">
                              <Clock className="h-3 w-3 text-gray-400 flex-shrink-0" />
                              <span className="text-xs text-gray-400 whitespace-nowrap">
                                {formatTimestamp(document.createdAt)}
                              </span>
                            </div>

                            {document.metadata?.category && (
                              <div className="flex items-center space-x-1">
                                <Tag className="h-3 w-3 text-gray-400 flex-shrink-0" />
                                <span className="text-xs text-gray-400 truncate max-w-[100px]">
                                  {document.metadata.category}
                                </span>
                              </div>
                            )}
                          </div>

                          {/* Content Preview */}
                          <p className="text-xs text-gray-300 mt-2 leading-relaxed">
                            <span className="line-clamp-2 break-words">
                              {document.content.substring(0, 120)}
                              {document.content.length > 120 && '...'}
                            </span>
                          </p>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center space-x-1 ml-2 flex-shrink-0">
                        {document.fileUrl && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              window.open(document.fileUrl, '_blank');
                            }}
                            className="w-6 h-6 p-0 text-gray-400 hover:text-white hover:bg-gray-600"
                            title="Download file"
                          >
                            <Download className="h-3 w-3" />
                          </Button>
                        )}

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleDocumentExpansion(document.id);
                          }}
                          className="w-6 h-6 p-0 text-gray-400 hover:text-white hover:bg-gray-600"
                          title="View details"
                        >
                          <Eye className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>

                    {/* Expanded Content */}
                    {isExpanded && (
                      <div className="mt-3 pt-3 border-t border-gray-600">
                        <div className="text-xs text-gray-300 max-h-40 overflow-y-auto bg-gray-800/50 rounded p-2 break-words">
                          {document.content}
                        </div>

                        {document.metadata && Object.keys(document.metadata).length > 0 && (
                          <div className="mt-2 pt-2 border-t border-gray-600">
                            <h5 className="text-xs font-medium text-gray-400 mb-1">Metadata:</h5>
                            <div className="space-y-1 max-h-20 overflow-y-auto">
                              {Object.entries(document.metadata).map(([key, value]) => (
                                <div key={key} className="flex text-xs">
                                  <span className="text-gray-400 flex-shrink-0 w-16">{key}:</span>
                                  <span className="text-gray-300 break-words ml-2 flex-1">
                                    {String(value)}
                                  </span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Footer - Fixed at bottom */}
      {documentContext.selectedDocuments.length > 0 && (
        <div className="p-4 border-t border-gray-700 bg-gray-800 flex-shrink-0">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <p className="text-sm text-gray-300">
                {documentContext.selectedDocuments.length} document{documentContext.selectedDocuments.length !== 1 ? 's' : ''} selected
              </p>
            </div>
            <p className="text-xs text-gray-400 leading-relaxed">
              Selected documents will provide context for more informed conversations with the agent
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
