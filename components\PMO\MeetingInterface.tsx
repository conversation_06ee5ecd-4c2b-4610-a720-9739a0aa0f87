"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '../ui/button';
import {
  Mi<PERSON>,
  MicOff,
  Phone,
  PhoneOff,
  Volume2,
  VolumeX,
  Activity,
  Loader2,
  AlertCircle,
  User,
  Bot,
  Bug
} from 'lucide-react';
import { PMOAgentVoiceConfig } from '../../lib/agents/voice/pmoAgentVoiceConfig';
import { MeetingState } from './AgentMeetingRoom';
import { usePMOVoiceConversation } from '../../lib/hooks/usePMOVoiceConversation';

interface MeetingInterfaceProps {
  meetingState: MeetingState;
  agentConfig: PMOAgentVoiceConfig | null;
  documentContext?: any[];
  onStartMeeting: () => void;
  onEndMeeting: () => void;
  onToggleMute: () => void;
  onVolumeChange: (volume: number) => void;
}

export default function MeetingInterface({
  meetingState,
  agentConfig,
  documentContext = [],
  onStartMeeting,
  onEndMeeting,
  onToggleMute,
  onVolumeChange
}: MeetingInterfaceProps) {
  const [userSpeaking, setUserSpeaking] = useState(false);
  const [agentSpeaking, setAgentSpeaking] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');

  // Voice conversation hook
  const voiceConversation = usePMOVoiceConversation({
    agentConfig,
    agentId: meetingState.agentId,
    documentContext,
    callbacks: {
      onConnect: () => {
        setConnectionStatus('connected');
        console.log('[MEETING_INTERFACE] Voice conversation connected');
      },
      onDisconnect: () => {
        setConnectionStatus('disconnected');
        console.log('[MEETING_INTERFACE] Voice conversation disconnected');
      },
      onError: (error) => {
        setConnectionStatus('disconnected');
        console.error('[MEETING_INTERFACE] Voice conversation error:', error);
      },
      onAgentSpeaking: (speaking) => {
        setAgentSpeaking(speaking);
      },
      onUserSpeaking: (speaking) => {
        setUserSpeaking(speaking);
      },
      onMessage: (message, isUser) => {
        console.log(`[MEETING_INTERFACE] ${isUser ? 'User' : 'Agent'}: ${message}`);
      }
    }
  });

  // Simulate voice activity (will be replaced with actual ElevenLabs integration)
  useEffect(() => {
    if (meetingState.isActive) {
      setConnectionStatus('connected');
      
      // Simulate periodic agent speaking
      const interval = setInterval(() => {
        if (Math.random() > 0.8) {
          setAgentSpeaking(true);
          setTimeout(() => setAgentSpeaking(false), 2000);
        }
      }, 5000);

      return () => clearInterval(interval);
    } else {
      setConnectionStatus('disconnected');
    }
  }, [meetingState.isActive]);

  // Update connection status based on meeting state
  useEffect(() => {
    if (meetingState.isConnecting) {
      setConnectionStatus('connecting');
    } else if (meetingState.isActive) {
      setConnectionStatus('connected');
    } else {
      setConnectionStatus('disconnected');
    }
  }, [meetingState.isConnecting, meetingState.isActive]);

  if (!agentConfig) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-gray-400">
          <AlertCircle className="h-12 w-12 mx-auto mb-4" />
          <p>No agent configuration available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* Meeting Status Bar */}
      <div className="bg-gray-800 px-6 py-3 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${
              connectionStatus === 'connected' ? 'bg-green-500' : 
              connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse' : 
              'bg-red-500'
            }`}></div>
            <span className="text-sm text-gray-300">
              {connectionStatus === 'connected' ? 'Connected' : 
               connectionStatus === 'connecting' ? 'Connecting...' : 
               'Disconnected'}
            </span>
          </div>
          
          {meetingState.connectionError && (
            <div className="flex items-center text-red-400 text-sm">
              <AlertCircle className="h-4 w-4 mr-1" />
              {meetingState.connectionError}
            </div>
          )}
        </div>
      </div>

      {/* Main Meeting Area */}
      <div className="flex-1 flex">
        {/* Agent Section (Top Half) */}
        <div className="flex-1 flex flex-col">
          <div className="flex-1 bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center relative">
            {/* Agent Avatar */}
            <div className="text-center">
              <div className={`
                w-32 h-32 rounded-full flex items-center justify-center mb-4 mx-auto
                ${agentConfig.color} 
                ${agentSpeaking ? 'animate-pulse ring-4 ring-white/30' : ''}
                transition-all duration-300
              `}>
                <Bot className="h-16 w-16 text-white" />
              </div>
              
              <h3 className="text-xl font-semibold text-white mb-2">
                {agentConfig.agentName}
              </h3>
              
              <p className="text-gray-400 mb-4">
                {agentConfig.description}
              </p>

              {/* Voice Activity Indicator */}
              {agentSpeaking && (
                <div className="flex items-center justify-center space-x-1">
                  <Activity className="h-4 w-4 text-green-400" />
                  <span className="text-green-400 text-sm">Speaking...</span>
                </div>
              )}
            </div>

            {/* Connection Status Overlay */}
            {meetingState.isConnecting && (
              <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                <div className="text-center text-white">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                  <p>Connecting to {agentConfig.agentName}...</p>
                </div>
              </div>
            )}
          </div>

          {/* User Section (Bottom Half) */}
          <div className="flex-1 bg-gray-900 flex items-center justify-center border-t border-gray-700">
            <div className="text-center">
              <div className={`
                w-24 h-24 rounded-full bg-gray-700 flex items-center justify-center mb-4 mx-auto
                ${userSpeaking ? 'ring-4 ring-blue-500/50 bg-blue-600' : ''}
                ${meetingState.isMuted ? 'bg-red-600' : ''}
                transition-all duration-300
              `}>
                <User className="h-12 w-12 text-white" />
              </div>
              
              <h3 className="text-lg font-medium text-white mb-2">You</h3>
              
              {/* User Status */}
              <div className="flex items-center justify-center space-x-2 text-sm">
                {meetingState.isMuted ? (
                  <>
                    <MicOff className="h-4 w-4 text-red-400" />
                    <span className="text-red-400">Muted</span>
                  </>
                ) : userSpeaking ? (
                  <>
                    <Activity className="h-4 w-4 text-blue-400" />
                    <span className="text-blue-400">Speaking...</span>
                  </>
                ) : (
                  <>
                    <Mic className="h-4 w-4 text-green-400" />
                    <span className="text-green-400">Ready</span>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Meeting Controls */}
      <div className="bg-gray-800 px-6 py-4 border-t border-gray-700">
        <div className="flex items-center justify-center space-x-4">
          {/* Mute/Unmute Button */}
          <Button
            variant={voiceConversation.state.isMuted ? "destructive" : "outline"}
            size="lg"
            onClick={() => {
              voiceConversation.toggleMute();
              onToggleMute();
            }}
            disabled={!voiceConversation.state.isConnected}
            className="w-14 h-14 rounded-full"
          >
            {voiceConversation.state.isMuted ? (
              <MicOff className="h-6 w-6" />
            ) : (
              <Mic className="h-6 w-6" />
            )}
          </Button>

          {/* Start/End Call Button */}
          {voiceConversation.state.isConnected ? (
            <Button
              variant="destructive"
              size="lg"
              onClick={async () => {
                await voiceConversation.endConversation();
                onEndMeeting();
              }}
              className="w-16 h-16 rounded-full bg-red-600 hover:bg-red-700"
            >
              <PhoneOff className="h-6 w-6" />
            </Button>
          ) : (
            <Button
              variant="default"
              size="lg"
              onClick={async () => {
                onStartMeeting();
                await voiceConversation.startConversation();
              }}
              disabled={voiceConversation.state.isConnecting || !voiceConversation.isReady}
              className="w-16 h-16 rounded-full bg-green-600 hover:bg-green-700"
            >
              {voiceConversation.state.isConnecting ? (
                <Loader2 className="h-6 w-6 animate-spin" />
              ) : (
                <Phone className="h-6 w-6" />
              )}
            </Button>
          )}

          {/* Volume Control */}
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                const newVolume = voiceConversation.state.volume > 0 ? 0 : 0.8;
                voiceConversation.setVolume(newVolume);
                onVolumeChange(newVolume);
              }}
              className="w-10 h-10 rounded-full"
            >
              {voiceConversation.state.volume > 0 ? (
                <Volume2 className="h-4 w-4" />
              ) : (
                <VolumeX className="h-4 w-4" />
              )}
            </Button>

            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={voiceConversation.state.volume}
              onChange={(e) => {
                const newVolume = parseFloat(e.target.value);
                voiceConversation.setVolume(newVolume);
                onVolumeChange(newVolume);
              }}
              className="w-20 h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
              disabled={!voiceConversation.state.isConnected}
            />
          </div>

          {/* Debug Audio Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              console.log('[MEETING_INTERFACE] Debug audio button clicked');
              voiceConversation.debugAudioOutput();
            }}
            className="w-10 h-10 rounded-full"
            title="Debug Audio Output"
          >
            <Bug className="h-4 w-4" />
          </Button>
        </div>

        {/* Meeting Instructions */}
        {!meetingState.isActive && (
          <div className="text-center mt-4">
            <p className="text-gray-400 text-sm">
              Click the green phone button to start your meeting with {agentConfig.agentName}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
