"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../app/context/AuthContext';
import { But<PERSON> } from '../ui/button';
import { 
  Mic, 
  MicOff, 
  Phone, 
  PhoneOff, 
  Volume2, 
  VolumeX,
  Settings,
  FileText,
  Users,
  Activity
} from 'lucide-react';
import { PMO_AGENT_VOICE_CONFIG, getPMOAgentConfig, generatePMOAgentId } from '../../lib/agents/voice/pmoAgentVoiceConfig';
import AgentSelectionPanel from './AgentSelectionPanel';
import MeetingInterface from './MeetingInterface';
import DocumentContextPanel from './DocumentContextPanel';

interface AgentMeetingRoomProps {
  className?: string;
}

export interface MeetingState {
  isActive: boolean;
  selectedAgentType: string | null;
  agentId: string | null;
  isMuted: boolean;
  volume: number;
  isConnecting: boolean;
  connectionError: string | null;
}

export interface DocumentContext {
  documents: any[];
  selectedDocuments: string[];
  isLoading: boolean;
}

export default function AgentMeetingRoom({ className = '' }: AgentMeetingRoomProps) {
  const { user } = useAuth();
  
  // Meeting state
  const [meetingState, setMeetingState] = useState<MeetingState>({
    isActive: false,
    selectedAgentType: null,
    agentId: null,
    isMuted: false,
    volume: 0.8,
    isConnecting: false,
    connectionError: null
  });

  // Document context state
  const [documentContext, setDocumentContext] = useState<DocumentContext>({
    documents: [],
    selectedDocuments: [],
    isLoading: false
  });

  // UI state
  const [showAgentSelection, setShowAgentSelection] = useState(true);
  const [showDocumentPanel, setShowDocumentPanel] = useState(true);

  /**
   * Load documents for the selected agent type
   */
  const loadAgentDocuments = useCallback(async (agentType: string) => {
    if (!user?.email) return;

    setDocumentContext(prev => ({ ...prev, isLoading: true }));

    try {
      console.log(`[MEETING_ROOM] Loading documents for agent type: ${agentType}, user: ${user.email}`);

      // First try the global endpoint with agent type filter
      const globalResponse = await fetch(`/api/agent-outputs/global?userId=${encodeURIComponent(user.email)}&agentType=${encodeURIComponent(agentType)}`);

      if (globalResponse.ok) {
        const globalData = await globalResponse.json();
        console.log(`[MEETING_ROOM] Global endpoint response:`, globalData);

        if (globalData.success && globalData.outputs && globalData.outputs.length > 0) {
          setDocumentContext(prev => ({
            ...prev,
            documents: globalData.outputs,
            isLoading: false
          }));
          console.log(`[MEETING_ROOM] Loaded ${globalData.outputs.length} documents for ${agentType} from global endpoint`);
          return;
        }
      } else {
        console.warn(`[MEETING_ROOM] Global endpoint failed with status ${globalResponse.status}: ${globalResponse.statusText}`);
      }

      // Fallback: Use main agent outputs endpoint and filter client-side
      console.log(`[MEETING_ROOM] Falling back to main agent outputs endpoint`);
      const mainResponse = await fetch('/api/agent-outputs?page=1&limit=100');

      if (mainResponse.ok) {
        const mainData = await mainResponse.json();
        console.log(`[MEETING_ROOM] Main endpoint returned ${mainData.results?.length || 0} total documents`);

        if (mainData.results && mainData.results.length > 0) {
          // Filter documents by agent type (case-insensitive and flexible matching)
          const filteredDocuments = mainData.results.filter((doc: any) => {
            const docAgentType = doc.agentType || '';
            // Exact match
            if (docAgentType === agentType) return true;
            // Case-insensitive match
            if (docAgentType.toLowerCase() === agentType.toLowerCase()) return true;
            // Partial match for compound agent types
            if (docAgentType.toLowerCase().includes(agentType.toLowerCase()) ||
                agentType.toLowerCase().includes(docAgentType.toLowerCase())) return true;
            return false;
          });

          // Transform documents to match expected format
          const transformedDocuments = filteredDocuments.map((doc: any) => ({
            id: doc.id,
            title: doc.prompt?.substring(0, 100) || doc.title || 'Agent Output',
            content: doc.result?.output || doc.result?.message || doc.content || '',
            agentType: doc.agentType || agentType,
            createdAt: doc.timestamp || doc.createdAt || new Date().toISOString(),
            updatedAt: doc.updatedAt || doc.timestamp || doc.createdAt || new Date().toISOString(),
            metadata: doc.metadata || {},
            category: doc.category || doc.metadata?.category,
            fileUrl: doc.result?.documentUrl || doc.fileUrl,
            summary: doc.result?.summary || (doc.content || '').substring(0, 200) + '...',
            tags: doc.tags || [],
            status: doc.status,
            projectId: doc.projectId || doc.metadata?.pmoId,
            teamName: doc.metadata?.teamName,
            assignedTeam: doc.metadata?.assignedTeam
          }));

          setDocumentContext(prev => ({
            ...prev,
            documents: transformedDocuments,
            isLoading: false
          }));
          console.log(`[MEETING_ROOM] Loaded ${transformedDocuments.length} filtered documents for ${agentType} from main endpoint`);
          return;
        }
      } else {
        console.error(`[MEETING_ROOM] Main endpoint failed with status ${mainResponse.status}: ${mainResponse.statusText}`);
      }

      // If both endpoints fail or return no data
      console.warn(`[MEETING_ROOM] No documents found for agent type: ${agentType}`);
      setDocumentContext(prev => ({ ...prev, documents: [], isLoading: false }));

    } catch (error) {
      console.error('[MEETING_ROOM] Error loading documents:', error);
      setDocumentContext(prev => ({ ...prev, documents: [], isLoading: false }));
    }
  }, [user?.email]);

  /**
   * Handle agent selection
   */
  const handleAgentSelect = useCallback(async (agentType: string) => {
    if (!user?.email) return;

    console.log(`[MEETING_ROOM] Agent selected: ${agentType}`);

    setMeetingState(prev => ({
      ...prev,
      selectedAgentType: agentType,
      agentId: generatePMOAgentId(user.email!, agentType),
      connectionError: null
    }));

    setShowAgentSelection(false);

    // Load documents for the selected agent type
    await loadAgentDocuments(agentType);
  }, [user?.email, loadAgentDocuments]);

  /**
   * Start meeting with selected agent
   */
  const startMeeting = useCallback(async () => {
    if (!meetingState.selectedAgentType || !meetingState.agentId) return;

    setMeetingState(prev => ({ ...prev, isConnecting: true, connectionError: null }));

    try {
      // Initialize ElevenLabs agent and conversation
      console.log(`[MEETING_ROOM] Starting meeting with ${meetingState.selectedAgentType}`);
      
      // TODO: Initialize ElevenLabs conversation
      // This will be implemented in the next step
      
      setMeetingState(prev => ({
        ...prev,
        isActive: true,
        isConnecting: false
      }));
      
    } catch (error) {
      console.error('[MEETING_ROOM] Error starting meeting:', error);
      setMeetingState(prev => ({
        ...prev,
        isConnecting: false,
        connectionError: error instanceof Error ? error.message : 'Failed to start meeting'
      }));
    }
  }, [meetingState.selectedAgentType, meetingState.agentId]);

  /**
   * End meeting
   */
  const endMeeting = useCallback(() => {
    console.log('[MEETING_ROOM] Ending meeting');
    
    setMeetingState(prev => ({
      ...prev,
      isActive: false,
      isConnecting: false,
      connectionError: null
    }));
  }, []);

  /**
   * Toggle mute
   */
  const toggleMute = useCallback(() => {
    setMeetingState(prev => ({
      ...prev,
      isMuted: !prev.isMuted
    }));
  }, []);

  /**
   * Adjust volume
   */
  const adjustVolume = useCallback((volume: number) => {
    setMeetingState(prev => ({
      ...prev,
      volume: Math.max(0, Math.min(1, volume))
    }));
  }, []);

  /**
   * Reset to agent selection
   */
  const resetToAgentSelection = useCallback(() => {
    setMeetingState({
      isActive: false,
      selectedAgentType: null,
      agentId: null,
      isMuted: false,
      volume: 0.8,
      isConnecting: false,
      connectionError: null
    });
    setShowAgentSelection(true);
    setDocumentContext({
      documents: [],
      selectedDocuments: [],
      isLoading: false
    });
  }, []);

  // Get selected agent configuration
  const selectedAgentConfig = meetingState.selectedAgentType 
    ? getPMOAgentConfig(meetingState.selectedAgentType)
    : null;

  return (
    <div className={`h-full flex flex-col bg-gray-900 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-700">
        <div className="flex items-center space-x-3">
          <Users className="h-6 w-6 text-purple-400" />
          <h2 className="text-xl font-semibold text-white">PMO Agent Meeting Room</h2>
          {selectedAgentConfig && (
            <span className={`px-3 py-1 rounded-full text-sm font-medium text-white ${selectedAgentConfig.color}`}>
              {selectedAgentConfig.agentName}
            </span>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowDocumentPanel(!showDocumentPanel)}
            className="text-gray-400 hover:text-white"
          >
            <FileText className="h-4 w-4 mr-1" />
            Documents
          </Button>
          
          {meetingState.selectedAgentType && (
            <Button
              variant="ghost"
              size="sm"
              onClick={resetToAgentSelection}
              className="text-gray-400 hover:text-white"
            >
              <Settings className="h-4 w-4 mr-1" />
              Change Agent
            </Button>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Left Side - Meeting Interface */}
        <div className="flex-1 flex flex-col">
          {showAgentSelection ? (
            <AgentSelectionPanel
              onAgentSelect={handleAgentSelect}
              availableAgents={Object.keys(PMO_AGENT_VOICE_CONFIG)}
              userId={user?.email}
              documentContext={documentContext.documents}
              selectedDocuments={documentContext.selectedDocuments}
              onLoadDocuments={loadAgentDocuments}
            />
          ) : (
            <MeetingInterface
              meetingState={meetingState}
              agentConfig={selectedAgentConfig}
              documentContext={documentContext.documents}
              onStartMeeting={startMeeting}
              onEndMeeting={endMeeting}
              onToggleMute={toggleMute}
              onVolumeChange={adjustVolume}
            />
          )}
        </div>

        {/* Right Side - Document Context Panel */}
        {showDocumentPanel && !showAgentSelection && (
          <div className="w-96 border-l border-gray-700">
            <DocumentContextPanel
              documentContext={documentContext}
              agentType={meetingState.selectedAgentType}
              onDocumentSelect={(documentIds) => {
                setDocumentContext(prev => ({
                  ...prev,
                  selectedDocuments: documentIds
                }));
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
}
