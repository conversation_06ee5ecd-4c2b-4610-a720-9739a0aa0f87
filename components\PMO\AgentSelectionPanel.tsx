"use client";

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '../ui/button';
import {
  <PERSON>,
  Mic,
  Brain,
  TrendingUp,
  Code,
  DollarSign,
  BarChart3,
  Search,
  ChevronRight,
  Volume2,
  Loader2,
  CheckCircle2
} from 'lucide-react';
import { getPMOAgentConfig, PMOAgentVoiceConfig } from '../../lib/agents/voice/pmoAgentVoiceConfig';
import { PMOAgentInitializationService } from '../../lib/services/PMOAgentInitializationService';
import { generateVoicePreview } from '../scriptreaderAI/voiceUtils';

interface AgentSelectionPanelProps {
  onAgentSelect: (agentType: string) => void;
  availableAgents: string[];
  userId?: string;
  documentContext?: any[];
  selectedDocuments?: string[];
  onLoadDocuments?: (agentType: string) => Promise<void>;
}

// Icon mapping for agent types
const AGENT_ICONS: Record<string, React.ComponentType<any>> = {
  'Marketing': TrendingUp,
  'Research': Search,
  'SoftwareDesign': Code,
  'Sales': DollarSign,
  'BusinessAnalysis': BarChart3,
  'InvestigativeResearch': Brain
};

export default function AgentSelectionPanel({
  onAgentSelect,
  availableAgents,
  userId,
  documentContext = [],
  selectedDocuments = [],
  onLoadDocuments
}: AgentSelectionPanelProps) {
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [playingPreview, setPlayingPreview] = useState<string | null>(null);
  const [initializingAgent, setInitializingAgent] = useState<string | null>(null);
  const [agentInitStatus, setAgentInitStatus] = useState<Record<string, 'ready' | 'initializing' | 'error'>>({});
  const [voicePreviewError, setVoicePreviewError] = useState<string | null>(null);

  // Audio refs for voice previews
  const audioRefs = useRef<Record<string, HTMLAudioElement>>({});

  // Set Marketing (Ag001) as default selection
  useEffect(() => {
    if (availableAgents.length > 0 && !selectedAgent) {
      // Default to Marketing agent (Ag001) if available
      const defaultAgent = availableAgents.includes('Marketing') ? 'Marketing' : availableAgents[0];
      setSelectedAgent(defaultAgent);
    }
  }, [availableAgents, selectedAgent]);

  // Cleanup effect to stop audio and prevent memory leaks
  useEffect(() => {
    return () => {
      // Stop all playing audio and clean up
      Object.values(audioRefs.current).forEach(audio => {
        if (audio) {
          audio.pause();
          audio.currentTime = 0;
          // Clean up any blob URLs
          if (audio.src && audio.src.startsWith('blob:')) {
            URL.revokeObjectURL(audio.src);
          }
        }
      });
      audioRefs.current = {};
    };
  }, []);

  /**
   * Handle agent selection
   */
  const handleAgentClick = async (agentType: string) => {
    setSelectedAgent(agentType);

    // Load documents for the selected agent type
    if (onLoadDocuments) {
      console.log(`[AGENT_SELECTION] Loading documents for ${agentType}`);
      try {
        await onLoadDocuments(agentType);
      } catch (error) {
        console.error(`[AGENT_SELECTION] Failed to load documents for ${agentType}:`, error);
      }
    }
  };

  /**
   * Confirm agent selection and start meeting
   */
  const confirmSelection = async () => {
    if (!selectedAgent || !userId) return;

    const agentConfig = getPMOAgentConfig(selectedAgent);
    if (!agentConfig) return;

    setInitializingAgent(selectedAgent);
    setAgentInitStatus(prev => ({ ...prev, [selectedAgent]: 'initializing' }));

    try {
      // Initialize agent with PMO Agent Initialization Service
      const initService = PMOAgentInitializationService.getInstance();
      const agentId = `${userId}-pmo-${selectedAgent.toLowerCase()}`;

      // Filter documents based on user selection
      const documentsToUpload = selectedDocuments.length > 0
        ? documentContext.filter(doc => selectedDocuments.includes(doc.id))
        : documentContext; // If no selection, use all documents

      console.log(`[AGENT_SELECTION] Uploading ${documentsToUpload.length} documents (${selectedDocuments.length > 0 ? 'selected' : 'all available'})`);

      const result = await initService.initializeAgent({
        userId,
        agentConfig,
        agentId,
        documentContext: documentsToUpload
      });

      if (result.success) {
        setAgentInitStatus(prev => ({ ...prev, [selectedAgent]: 'ready' }));
        console.log(`[AGENT_SELECTION] Agent ${selectedAgent} initialized successfully`);

        // Proceed with agent selection
        onAgentSelect(selectedAgent);
      } else {
        setAgentInitStatus(prev => ({ ...prev, [selectedAgent]: 'error' }));
        console.error(`[AGENT_SELECTION] Agent initialization failed:`, result.error);
      }
    } catch (error) {
      console.error(`[AGENT_SELECTION] Error during agent initialization:`, error);
      setAgentInitStatus(prev => ({ ...prev, [selectedAgent]: 'error' }));
    } finally {
      setInitializingAgent(null);
    }
  };

  /**
   * Check browser audio support
   */
  const checkAudioSupport = (): boolean => {
    try {
      const audio = new Audio();
      return !!(audio.canPlayType && audio.canPlayType('audio/mpeg').replace(/no/, ''));
    } catch (error) {
      return false;
    }
  };

  /**
   * Play voice preview for agent using ElevenLabs TTS
   */
  const playVoicePreview = async (agentType: string) => {
    console.log(`[AGENT_SELECTION] Starting voice preview for agent: ${agentType}`);

    const config = getPMOAgentConfig(agentType);
    if (!config) {
      console.error(`[AGENT_SELECTION] No config found for agent type: ${agentType}`);
      return;
    }

    console.log(`[AGENT_SELECTION] Agent config:`, config);

    // Check browser audio support
    if (!checkAudioSupport()) {
      console.error(`[AGENT_SELECTION] Browser audio support check failed`);
      setVoicePreviewError('Audio playback not supported in this browser');
      return;
    }

    console.log(`[AGENT_SELECTION] Browser audio support check passed`);

    // Stop any currently playing preview
    if (playingPreview && audioRefs.current[playingPreview]) {
      audioRefs.current[playingPreview].pause();
      audioRefs.current[playingPreview].currentTime = 0;
    }

    setPlayingPreview(agentType);
    setVoicePreviewError(null);

    try {
      // Generate preview text specific to the agent
      const previewText = `Hello, I'm ${config.agentName}, your ${config.agentType} expert. I'm here to help you with strategic planning and project guidance.`;

      console.log(`[AGENT_SELECTION] Generating voice preview for ${agentType} with voice ID: ${config.voiceId}`);

      // Generate audio using ElevenLabs TTS
      console.log(`[AGENT_SELECTION] Calling generateVoicePreview with voiceId: ${config.voiceId}, text: "${previewText}"`);
      const audioUrl = await generateVoicePreview(config.voiceId, previewText);
      console.log(`[AGENT_SELECTION] Generated audio URL: ${audioUrl ? 'success' : 'failed'}`);
      console.log(`[AGENT_SELECTION] Audio URL length: ${audioUrl?.length || 0}`);

      // Create and configure audio element
      const audio = new Audio(audioUrl);
      audioRefs.current[agentType] = audio;

      // Set up audio event handlers
      audio.onended = () => {
        setPlayingPreview(null);
        // Clean up the blob URL to prevent memory leaks
        URL.revokeObjectURL(audioUrl);
      };

      audio.onerror = (e) => {
        console.error('[AGENT_SELECTION] Audio playback error:', {
          error: e,
          audioSrc: audio.src,
          audioReadyState: audio.readyState,
          audioNetworkState: audio.networkState,
          audioError: audio.error,
          agentType,
          voiceId: config.voiceId,
          audioUrlValid: !!audioUrl,
          audioUrlLength: audioUrl?.length || 0
        });

        // Provide more specific error message based on the error
        let errorMessage = 'Failed to play voice preview.';
        if (audio.error) {
          switch (audio.error.code) {
            case MediaError.MEDIA_ERR_ABORTED:
              errorMessage = 'Audio playback was aborted.';
              break;
            case MediaError.MEDIA_ERR_NETWORK:
              errorMessage = 'Network error while loading audio.';
              break;
            case MediaError.MEDIA_ERR_DECODE:
              errorMessage = 'Audio file is corrupted or invalid format.';
              break;
            case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
              errorMessage = 'Audio format not supported by browser.';
              break;
            default:
              errorMessage = `Audio error (code: ${audio.error.code})`;
          }
        }

        setVoicePreviewError(errorMessage + ' Please check your browser audio settings.');
        setPlayingPreview(null);
        URL.revokeObjectURL(audioUrl);
      };

      // Handle browser autoplay restrictions
      try {
        await audio.play();
        console.log(`[AGENT_SELECTION] Successfully playing voice preview for ${agentType}`);
      } catch (playError) {
        console.error('[AGENT_SELECTION] Audio play error (likely autoplay restriction):', {
          error: playError,
          audioSrc: audio.src,
          audioReadyState: audio.readyState,
          audioNetworkState: audio.networkState,
          agentType,
          voiceId: config.voiceId
        });
        setVoicePreviewError('Audio blocked by browser. Please click again to play.');
        setPlayingPreview(null);
        URL.revokeObjectURL(audioUrl);
      }

    } catch (error) {
      console.error('[AGENT_SELECTION] Error generating voice preview:', error);

      // Provide more specific error messages
      let errorMessage = 'Failed to generate voice preview';
      if (error instanceof Error) {
        if (error.message.includes('Failed to fetch')) {
          errorMessage = 'Network error: Unable to connect to voice service';
        } else if (error.message.includes('401') || error.message.includes('403')) {
          errorMessage = 'Voice service authentication failed';
        } else if (error.message.includes('429')) {
          errorMessage = 'Voice service rate limit exceeded. Please try again later.';
        } else {
          errorMessage = `Voice preview failed: ${error.message}`;
        }
      }

      setVoicePreviewError(errorMessage);
      setPlayingPreview(null);
    }
  };

  /**
   * Stop voice preview playback
   */
  const stopVoicePreview = () => {
    if (playingPreview && audioRefs.current[playingPreview]) {
      audioRefs.current[playingPreview].pause();
      audioRefs.current[playingPreview].currentTime = 0;
      setPlayingPreview(null);
    }
  };

  return (
    <div className="flex-1 flex flex-col items-center justify-center p-8 bg-gradient-to-br from-gray-900 to-gray-800">
      <div className="max-w-4xl w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Users className="h-12 w-12 text-purple-400 mr-3" />
            <h1 className="text-3xl font-bold text-white">Select Your PMO Agent</h1>
          </div>
          <p className="text-gray-400 text-lg">
            Choose an expert agent to start your voice-enabled meeting session
          </p>
        </div>

        {/* Agent Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {availableAgents.map((agentType) => {
            const config = getPMOAgentConfig(agentType);
            if (!config) return null;

            const IconComponent = AGENT_ICONS[agentType] || Users;
            const isSelected = selectedAgent === agentType;
            const isPlayingPreview = playingPreview === agentType;
            const isInitializing = initializingAgent === agentType;
            const initStatus = agentInitStatus[agentType];

            return (
              <div
                key={agentType}
                className={`
                  relative p-6 rounded-lg border-2 cursor-pointer transition-all duration-200
                  ${isSelected 
                    ? 'border-purple-500 bg-purple-500/10 shadow-lg shadow-purple-500/20' 
                    : 'border-gray-600 bg-gray-800/50 hover:border-gray-500 hover:bg-gray-800'
                  }
                `}
                onClick={() => handleAgentClick(agentType)}
              >
                {/* Agent Icon and Name */}
                <div className="flex items-center mb-4">
                  <div className={`p-3 rounded-lg ${config.color} mr-3`}>
                    <IconComponent className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">{config.agentName}</h3>
                    <p className="text-sm text-gray-400">{config.agentId}</p>
                  </div>
                </div>

                {/* Description */}
                <p className="text-gray-300 text-sm mb-4 line-clamp-2">
                  {config.description}
                </p>

                {/* Specializations */}
                <div className="mb-4">
                  <h4 className="text-xs font-medium text-gray-400 mb-2">SPECIALIZATIONS</h4>
                  <div className="flex flex-wrap gap-1">
                    {config.specialization.slice(0, 3).map((spec, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded"
                      >
                        {spec}
                      </span>
                    ))}
                    {config.specialization.length > 3 && (
                      <span className="px-2 py-1 bg-gray-700 text-gray-400 text-xs rounded">
                        +{config.specialization.length - 3} more
                      </span>
                    )}
                  </div>
                </div>

                {/* Voice Preview Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    if (isPlayingPreview) {
                      stopVoicePreview();
                    } else {
                      playVoicePreview(agentType);
                    }
                  }}
                  className="w-full text-gray-400 hover:text-white border border-gray-600 hover:border-gray-500"
                >
                  {isPlayingPreview ? (
                    <>
                      <div className="animate-pulse flex items-center">
                        <Volume2 className="h-4 w-4 mr-2" />
                        Stop Preview
                      </div>
                    </>
                  ) : (
                    <>
                      <Mic className="h-4 w-4 mr-2" />
                      Voice Preview
                    </>
                  )}
                </Button>

                {/* Voice Preview Error */}
                {voicePreviewError && (
                  <div className="mt-2 p-2 bg-red-900/50 border border-red-600 rounded text-red-300 text-xs">
                    {voicePreviewError}
                  </div>
                )}

                {/* Status Indicators */}
                <div className="absolute top-4 right-4 flex items-center space-x-2">
                  {/* Initialization Status */}
                  {isInitializing && (
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                      <Loader2 className="h-3 w-3 text-white animate-spin" />
                    </div>
                  )}
                  {initStatus === 'ready' && (
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                      <CheckCircle2 className="h-3 w-3 text-white" />
                    </div>
                  )}
                  {initStatus === 'error' && (
                    <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">!</span>
                    </div>
                  )}

                  {/* Selection Indicator */}
                  {isSelected && (
                    <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                      <ChevronRight className="h-4 w-4 text-white" />
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Selected Agent Details */}
        {selectedAgent && (
          <div className="bg-gray-800 rounded-lg p-6 mb-6">
            <h3 className="text-lg font-semibold text-white mb-3">Selected Agent Details</h3>
            {(() => {
              const config = getPMOAgentConfig(selectedAgent);
              if (!config) return null;

              return (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-400 mb-2">AGENT INFORMATION</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-300">Name:</span>
                        <span className="text-white">{config.agentName}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-300">Type:</span>
                        <span className="text-white">{config.agentType}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-300">Voice:</span>
                        <span className="text-white">{config.voiceName}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium text-gray-400 mb-2">EXPERTISE AREAS</h4>
                    <div className="space-y-1">
                      {config.specialization.map((spec, index) => (
                        <div key={index} className="flex items-center">
                          <div className="w-2 h-2 bg-purple-400 rounded-full mr-2"></div>
                          <span className="text-gray-300 text-sm">{spec}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              );
            })()}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-center space-x-4">
          <Button
            onClick={confirmSelection}
            disabled={!selectedAgent || !userId || initializingAgent !== null}
            className="px-8 py-3 bg-purple-600 hover:bg-purple-700 text-white font-medium"
          >
            {initializingAgent ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Initializing {getPMOAgentConfig(initializingAgent)?.agentName}...
              </>
            ) : selectedAgent && agentInitStatus[selectedAgent] === 'ready' ? (
              <>
                <CheckCircle2 className="h-4 w-4 mr-2" />
                Start Meeting with {getPMOAgentConfig(selectedAgent)?.agentName}
              </>
            ) : selectedAgent && agentInitStatus[selectedAgent] === 'error' ? (
              'Retry Initialization'
            ) : (
              `Initialize & Start Meeting with ${selectedAgent ? getPMOAgentConfig(selectedAgent)?.agentName : 'Agent'}`
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
