/**
 * User Agent Storage and Management
 * 
 * This module provides utilities for storing and managing user-agent relationships
 * in Firebase Firestore, including ElevenLabs agent IDs and their status.
 */

import { 
  collection, 
  doc, 
  setDoc, 
  getDoc, 
  getDocs, 
  updateDoc, 
  query, 
  where, 
  orderBy, 
  serverTimestamp,
  Timestamp 
} from 'firebase/firestore';
import { db } from '../../components/firebase/config';

export interface UserAgent {
  id: string;
  userId: string;
  agentId: string; // ElevenLabs agent ID
  agentName: string;
  voiceId: string;
  status: 'active' | 'inactive' | 'error';
  createdAt: Date;
  updatedAt: Date;
  lastVerified?: Date;
  knowledgeBaseId?: string;
  conversationId?: string;
  metadata?: {
    prompt?: string;
    documentsUploaded?: number;
    lastKnowledgeBaseUpdate?: Date;
    [key: string]: any;
  };
}

export interface CreateUserAgentData {
  userId: string;
  agentId: string;
  agentName: string;
  voiceId: string;
  knowledgeBaseId?: string;
  conversationId?: string;
  metadata?: Record<string, any>;
}

/**
 * Get the user agents collection reference
 */
const getUserAgentsCollection = () => collection(db, 'userAgents');

/**
 * Store a new user agent relationship in the database
 */
export async function storeUserAgent(data: CreateUserAgentData): Promise<string> {
  try {
    const userAgentsRef = getUserAgentsCollection();
    const docRef = doc(userAgentsRef);
    
    const userAgentData: Omit<UserAgent, 'id'> = {
      userId: data.userId,
      agentId: data.agentId,
      agentName: data.agentName,
      voiceId: data.voiceId,
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
      lastVerified: new Date(),
      knowledgeBaseId: data.knowledgeBaseId,
      conversationId: data.conversationId,
      metadata: data.metadata || {}
    };

    await setDoc(docRef, {
      ...userAgentData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      lastVerified: serverTimestamp()
    });

    console.log(`[USER_AGENTS] Stored user agent: ${data.agentId} for user: ${data.userId}`);
    return docRef.id;
  } catch (error) {
    console.error('[USER_AGENTS] Error storing user agent:', error);
    throw error;
  }
}

/**
 * Get user agent by user ID (returns the most recent active agent)
 */
export async function getUserAgent(userId: string): Promise<UserAgent | null> {
  try {
    const userAgentsRef = getUserAgentsCollection();
    const q = query(
      userAgentsRef,
      where('userId', '==', userId),
      where('status', '==', 'active'),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return null;
    }

    const doc = querySnapshot.docs[0];
    const data = doc.data();
    
    return {
      id: doc.id,
      userId: data.userId,
      agentId: data.agentId,
      agentName: data.agentName,
      voiceId: data.voiceId,
      status: data.status,
      createdAt: data.createdAt?.toDate() || new Date(),
      updatedAt: data.updatedAt?.toDate() || new Date(),
      lastVerified: data.lastVerified?.toDate(),
      knowledgeBaseId: data.knowledgeBaseId,
      conversationId: data.conversationId,
      metadata: data.metadata || {}
    };
  } catch (error) {
    console.error('[USER_AGENTS] Error getting user agent:', error);
    throw error;
  }
}

/**
 * Get user agent by agent ID
 */
export async function getUserAgentByAgentId(agentId: string): Promise<UserAgent | null> {
  try {
    const userAgentsRef = getUserAgentsCollection();
    const q = query(userAgentsRef, where('agentId', '==', agentId));

    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return null;
    }

    const doc = querySnapshot.docs[0];
    const data = doc.data();
    
    return {
      id: doc.id,
      userId: data.userId,
      agentId: data.agentId,
      agentName: data.agentName,
      voiceId: data.voiceId,
      status: data.status,
      createdAt: data.createdAt?.toDate() || new Date(),
      updatedAt: data.updatedAt?.toDate() || new Date(),
      lastVerified: data.lastVerified?.toDate(),
      knowledgeBaseId: data.knowledgeBaseId,
      conversationId: data.conversationId,
      metadata: data.metadata || {}
    };
  } catch (error) {
    console.error('[USER_AGENTS] Error getting user agent by agent ID:', error);
    throw error;
  }
}

/**
 * Update user agent status and metadata
 */
export async function updateUserAgent(
  userAgentId: string, 
  updates: Partial<Pick<UserAgent, 'status' | 'lastVerified' | 'knowledgeBaseId' | 'conversationId' | 'metadata'>>
): Promise<void> {
  try {
    const userAgentRef = doc(getUserAgentsCollection(), userAgentId);
    
    await updateDoc(userAgentRef, {
      ...updates,
      updatedAt: serverTimestamp(),
      ...(updates.lastVerified && { lastVerified: serverTimestamp() })
    });

    console.log(`[USER_AGENTS] Updated user agent: ${userAgentId}`);
  } catch (error) {
    console.error('[USER_AGENTS] Error updating user agent:', error);
    throw error;
  }
}

/**
 * Mark user agent as inactive (soft delete)
 */
export async function deactivateUserAgent(userAgentId: string): Promise<void> {
  try {
    await updateUserAgent(userAgentId, { status: 'inactive' });
    console.log(`[USER_AGENTS] Deactivated user agent: ${userAgentId}`);
  } catch (error) {
    console.error('[USER_AGENTS] Error deactivating user agent:', error);
    throw error;
  }
}

/**
 * Get all user agents for a user (including inactive ones)
 */
export async function getAllUserAgents(userId: string): Promise<UserAgent[]> {
  try {
    const userAgentsRef = getUserAgentsCollection();
    const q = query(
      userAgentsRef,
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        userId: data.userId,
        agentId: data.agentId,
        agentName: data.agentName,
        voiceId: data.voiceId,
        status: data.status,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
        lastVerified: data.lastVerified?.toDate(),
        knowledgeBaseId: data.knowledgeBaseId,
        conversationId: data.conversationId,
        metadata: data.metadata || {}
      };
    });
  } catch (error) {
    console.error('[USER_AGENTS] Error getting all user agents:', error);
    throw error;
  }
}

/**
 * Check if user has any active agents
 */
export async function hasActiveAgent(userId: string): Promise<boolean> {
  try {
    const userAgent = await getUserAgent(userId);
    return userAgent !== null;
  } catch (error) {
    console.error('[USER_AGENTS] Error checking for active agent:', error);
    return false;
  }
}
