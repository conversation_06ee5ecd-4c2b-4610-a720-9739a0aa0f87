import { ElevenLabsClient } from "elevenlabs";

/**
 * Type definition for ElevenLabs knowledge base document response
 * Matches the return structure of addToKnowledgeBase
 */
interface KnowledgeBaseDocumentResponse {
  id: string;
  prompt_injectable: boolean;
}

/**
 * Type definition for ElevenLabs RAG index response
 * Matches the return structure of computeRagIndex
 */
interface RagIndexResponse {
  status: "created" | "processing" | "failed" | "succeeded";
  progress_percentage: number;
}

/**
 * Creates and configures an instance of the ElevenLabs client
 * @param apiKey - Optional API key to override the environment variable
 * @returns Configured ElevenLabs client instance
 */
export function createElevenLabsClient(apiKey?: string): ElevenLabsClient {
  const key = apiKey || process.env.ELEVENLABS_API_KEY || '***************************************************';
  if (!key) {
    throw new Error("ElevenLabs API key is required. Set ELEVENLABS_API_KEY in your environment variables.");
  }
  return new ElevenLabsClient({ apiKey: key });
}

export async function configureAgentClientTools(agentId: string, apiKey?: string): Promise<Record<string, any>> {
  try {
    const client = createElevenLabsClient(apiKey);
    const currentConfig = await getAgentConfiguration(agentId, apiKey);

    // Define the script readiness acknowledgment client tool
    const scriptReadinessTool = {
      name: "switch_to_script_tab",
      type: "client",
      description: "Call this function when the user confirms they are ready to begin rehearsal. This will acknowledge their readiness and inform them to manually navigate to the Script tab if they want to view their script content during rehearsal.",
      parameters: {
        type: "object",
        properties: {
          ready: {
            type: "boolean",
            description: "Set to true when user has confirmed they are ready to begin rehearsal",
            default: true
          }
        },
        required: ["ready"]
      },
      wait_for_response: true
    };

    // Get existing tools and filter out any existing switch_to_script_tab tools
    const existingTools = currentConfig.conversation_config?.tools || [];
    const filteredTools = existingTools.filter((tool: any) =>
      tool.name !== 'switch_to_script_tab' && tool.name !== 'load_script_for_rehearsal'
    );

    // Add the new tool
    const updatedTools = [...filteredTools, scriptReadinessTool];

    // Update the agent configuration
    const patchBody = {
      conversation_config: {
        ...currentConfig.conversation_config,
        tools: updatedTools
      }
    };

    const updateResult = await client.conversationalAi.updateAgent(agentId, patchBody);

    // Wait for propagation
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Verify the update
    const verificationConfig = await getAgentConfiguration(agentId, apiKey);
    const toolsAfterUpdate = verificationConfig.conversation_config?.tools || [];
    const hasScriptTool = toolsAfterUpdate.some((tool: any) => tool.name === 'switch_to_script_tab');

    return updateResult;
  } catch (error) {
    console.error(`[ELEVENLABS] ❌ Error configuring client tools for agent ${agentId}:`, error);
    throw error;
  }
}

/**
 * Uploads a document to ElevenLabs knowledge base
 * 
 * This function fetches a file from a URL and uploads it to the ElevenLabs
 * knowledge base system, making it available for association with agents.
 * 
 * @param fileUrl - URL of the file to upload
 * @param fileName - Name of the file for identification
 * @param fileType - MIME type of the file
 * @param apiKey - Optional API key to override the environment variable
 * @returns Knowledge base document ID and metadata
 * @throws Error if file type is unsupported, URL fetching fails, or API errors occur
 */
export async function uploadToKnowledgeBase(
  fileUrl: string,
  fileName: string,
  fileType: string,
  apiKey?: string
): Promise<KnowledgeBaseDocumentResponse> {
  try {
    const client = createElevenLabsClient(apiKey);

    // Validate file type against ElevenLabs supported formats
    const supportedTypes = [
      "application/pdf",
      "text/plain",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "text/markdown",
    ];

    if (!supportedTypes.includes(fileType)) {
      throw new Error(`Unsupported file type: ${fileType}. Supported types: ${supportedTypes.join(", ")}`);
    }

    // Fetch the file from the URL with error handling
    let fileResponse;
    try {
      fileResponse = await fetch(fileUrl);
      if (!fileResponse.ok) {
        throw new Error(`HTTP error ${fileResponse.status}: ${fileResponse.statusText}`);
      }
    } catch (fetchError) {
      throw new Error(`Failed to fetch file: ${fetchError instanceof Error ? fetchError.message : String(fetchError)}`);
    }

    // Convert response to blob and validate
    let fileBlob;
    try {
      fileBlob = await fileResponse.blob();
      if (fileBlob.size === 0) {
        throw new Error("Fetched file is empty (0 bytes)");
      }
    } catch (blobError) {
      throw new Error(`Failed to process file data: ${blobError instanceof Error ? blobError.message : String(blobError)}`);
    }

    // Create File object and upload to knowledge base
    const file = new File([fileBlob], fileName, { type: fileType });

    try {
      // Call the ElevenLabs API to add to knowledge base
      const response = await client.conversationalAi.addToKnowledgeBase({
        file,
        name: fileName,
      });

      // Validate response
      if (!response || !response.id) {
        throw new Error("Invalid response from ElevenLabs API: Missing document ID");
      }

      return {
        id: response.id,
        prompt_injectable: response.prompt_injectable || false,
      };
    } catch (apiError) {
      // Format API errors for better debugging
      if (apiError instanceof Error) {
        throw new Error(`ElevenLabs API error: ${apiError.message}`);
      }
      throw new Error(`Unknown error from ElevenLabs API: ${String(apiError)}`);
    }
  } catch (error) {
    console.error("[ELEVENLABS] Error uploading to ElevenLabs Knowledge Base:", error);
    throw error;
  }
}

/**
 * Triggers RAG indexing for a knowledge base document
 * 
 * This function initiates RAG indexing for the specified document and waits until
 * the indexing process is complete (status: succeeded).
 * 
 * @param documentationId - ID of the knowledge base document to index
 * @param apiKey - Optional API key to override the environment variable
 * @param forceReindex - Whether to force reindexing if the document is already indexed (default: false)
 * @param model - The model to use for RAG indexing (default: "e5_mistral_7b_instruct")
 * @returns RAG index status and progress
 * @throws Error if RAG indexing fails or times out
 */
export async function computeRagIndex(
  documentationId: string,
  apiKey?: string,
  forceReindex: boolean = false,
  model: "e5_mistral_7b_instruct" | "gte_Qwen2_15B_instruct" = "e5_mistral_7b_instruct"
): Promise<RagIndexResponse> {
  try {
    // Validate input parameters
    if (!documentationId || typeof documentationId !== 'string') {
      throw new Error("Documentation ID is required and must be a string");
    }

    const apiKeyToUse = apiKey || process.env.ELEVENLABS_API_KEY || '***************************************************';

    // Construct the URL with the force_reindex query parameter
    const url = new URL(`https://api.elevenlabs.io/v1/convai/knowledge-base/${encodeURIComponent(documentationId)}/rag-index`);
    url.searchParams.append("force_reindex", forceReindex.toString());

    // Log the attempt
    console.log(`[ELEVENLABS] Triggering RAG indexing for document ${documentationId} with model ${model}${forceReindex ? " (force reindex)" : ""}`);

    // Initial request to trigger RAG indexing
    const initialResponse = await fetch(url.toString(), {
      method: "POST",
      headers: {
        "xi-api-key": apiKeyToUse,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ model }),
    });

    if (!initialResponse.ok) {
      const errorText = await initialResponse.text();
      throw new Error(`Failed to trigger RAG indexing: ${initialResponse.status} ${initialResponse.statusText} - ${errorText}`);
    }

    let result: RagIndexResponse = await initialResponse.json();
    console.log(`[ELEVENLABS] RAG indexing initiated:`, result);

    // If the status is already "succeeded", return immediately
    if (result.status === "succeeded") {
      console.log(`[ELEVENLABS] RAG indexing already completed for document ${documentationId}`);
      return result;
    }

    // If the status is "failed", throw an error
    if (result.status === "failed") {
      throw new Error(`RAG indexing failed for document ${documentationId}`);
    }

    // Poll the status until it becomes "succeeded" or "failed"
    const maxAttempts = 30; // Maximum number of polling attempts (30 * 5s = 150s timeout)
    const pollingInterval = 5000; // Poll every 5 seconds
    let attempts = 0;

    while (attempts < maxAttempts) {
      attempts++;
      await new Promise(resolve => setTimeout(resolve, pollingInterval));

      const statusResponse = await fetch(url.toString(), {
        method: "POST",
        headers: {
          "xi-api-key": apiKeyToUse,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ model }),
      });

      if (!statusResponse.ok) {
        const errorText = await statusResponse.text();
        throw new Error(`Failed to check RAG indexing status: ${statusResponse.status} ${statusResponse.statusText} - ${errorText}`);
      }

      result = await statusResponse.json();
      console.log(`[ELEVENLABS] RAG indexing status (attempt ${attempts}/${maxAttempts}):`, result);

      if (result.status === "succeeded") {
        console.log(`[ELEVENLABS] RAG indexing completed successfully for document ${documentationId}`);
        return result;
      }

      if (result.status === "failed") {
        throw new Error(`RAG indexing failed for document ${documentationId}`);
      }

      // Continue polling if status is "created" or "processing"
    }

    throw new Error(`RAG indexing for document ${documentationId} did not complete within the expected time (${maxAttempts * pollingInterval / 1000} seconds)`);
  } catch (error) {
    console.error("[ELEVENLABS] Error during RAG indexing:", error);
    throw error;
  }
}

/**
 * Fetches the agent's configuration to inspect its settings
 * 
 * @param agentId - ID of the agent to fetch
 * @param apiKey - Optional API key to override the environment variable
 * @returns Agent configuration
 * @throws Error if the fetch fails
 */
export async function getAgentConfiguration(agentId: string, apiKey?: string): Promise<any> {
  try {
    if (!agentId || typeof agentId !== 'string') {
      throw new Error("Agent ID is required and must be a string");
    }

    const apiKeyToUse = apiKey || process.env.ELEVENLABS_API_KEY || '***************************************************';

    const response = await fetch(
      `https://api.elevenlabs.io/v1/convai/agents/${encodeURIComponent(agentId)}`,
      {
        method: "GET",
        headers: {
          "xi-api-key": apiKeyToUse,
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to fetch agent configuration: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    console.log(`[ELEVENLABS] Fetched agent configuration for agent ${agentId}:`, result);
    return result;
  } catch (error) {
    console.error(`[ELEVENLABS] Error fetching agent configuration for agent ${agentId}:`, error);
    throw error;
  }
}

/**
 * Associates a knowledge base document with an agent
 * 
 * This function ensures the document is RAG indexed and then updates the agent
 * to include the document in its knowledge base using the document_ids field.
 * 
 * @param agentId - ID of the agent to associate with the document
 * @param knowledgeBaseDocId - ID of the knowledge base document
 * @param apiKey - Optional API key to override the environment variable
 * @returns Result of the agent update operation
 * @throws Error if RAG indexing or agent update fails
 */
export async function updateAgentKnowledgeBase(
  agentId: string,
  knowledgeBaseDocId: string,
  apiKey?: string
): Promise<any> {
  try {
    // Validate input parameters
    if (!agentId || typeof agentId !== 'string') {
      throw new Error("Agent ID is required and must be a string");
    }

    if (!knowledgeBaseDocId || typeof knowledgeBaseDocId !== 'string') {
      throw new Error("Knowledge base document ID is required and must be a string");
    }

    // Step 1: Ensure the document is RAG indexed
    console.log(`[ELEVENLABS] Ensuring RAG indexing for document ${knowledgeBaseDocId} before associating with agent ${agentId}`);
    const ragIndexResult = await computeRagIndex(knowledgeBaseDocId, apiKey, true); // Force reindexing to ensure proper indexing
    if (ragIndexResult.status !== "succeeded") {
      throw new Error(`Cannot associate document ${knowledgeBaseDocId} with agent ${agentId}: RAG indexing did not succeed`);
    }

    // Step 2: Fetch the current agent configuration to preserve existing settings
    const currentConfig = await getAgentConfiguration(agentId, apiKey);
    const currentConversationConfig = currentConfig.conversation_config || {};
    const currentDocumentIds = currentConfig.document_ids || [];

    // Step 3: Update the agent to include the knowledge base document
    const apiKeyToUse = apiKey || process.env.ELEVENLABS_API_KEY || '***************************************************';

    console.log(`[ELEVENLABS] Attempting to update agent ${agentId} to include knowledge base document ${knowledgeBaseDocId}`);
    console.log(`[ELEVENLABS] Current document IDs:`, currentDocumentIds);

    // Add the new document ID to existing ones (avoid duplicates)
    const updatedDocumentIds = [...new Set([...currentDocumentIds, knowledgeBaseDocId])];
    console.log(`[ELEVENLABS] Updated document IDs:`, updatedDocumentIds);

    // Retry mechanism to mimic the "SAVE" action
    const maxRetries = 3;
    let attempt = 0;
    let updateResult = null;

    while (attempt < maxRetries) {
      attempt++;
      try {
        const updateResponse = await fetch(
          `https://api.elevenlabs.io/v1/convai/agents/${encodeURIComponent(agentId)}`,
          {
            method: "PATCH",
            headers: {
              "xi-api-key": apiKeyToUse,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              document_ids: updatedDocumentIds,
              conversation_config: {
                ...currentConversationConfig, // Preserve existing settings
                rag_enabled: true, // Enable RAG usage
              },
              // Hypothetical field to mimic the "SAVE" action (if required by the API)
              apply_changes: true,
            }),
          }
        );

        if (!updateResponse.ok) {
          const errorText = await updateResponse.text();
          throw new Error(`Failed to update agent (attempt ${attempt}/${maxRetries}): ${updateResponse.status} ${updateResponse.statusText} - ${errorText}`);
        }

        updateResult = await updateResponse.json();
        console.log(`[ELEVENLABS] Successfully updated agent with document_ids and RAG settings (attempt ${attempt}/${maxRetries})`, updateResult);
        break; // Exit the retry loop on success
      } catch (error) {
        if (attempt === maxRetries) {
          throw error; // Re-throw the error if all retries fail
        }
        console.warn(`[ELEVENLABS] Update attempt ${attempt}/${maxRetries} failed, retrying after delay...`, error);
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds before retrying
      }
    }

    // Step 4: Fetch the agent's configuration again to verify the update
    const updatedAgentConfig = await getAgentConfiguration(agentId, apiKey);
    console.log(`[ELEVENLABS] Agent ${agentId} configuration after update:`, updatedAgentConfig);

    console.log(`[ELEVENLABS] Document ${knowledgeBaseDocId} associated with agent ${agentId} and RAG enabled. Please verify the association in the ElevenLabs dashboard and test the agent in rehearsal.`);
    return updateResult;
  } catch (error) {
    console.error("[ELEVENLABS] Error updating agent knowledge base:", error);
    throw new Error(
      `Failed to associate knowledge base document ${knowledgeBaseDocId} with agent ${agentId}. ` +
      `Error: ${error instanceof Error ? error.message : String(error)}. ` +
      `Manual association may be required via the ElevenLabs dashboard.`
    );
  }
}

/**
 * Retrieves a list of all knowledge base documents
 * 
 * @param apiKey - Optional API key to override the environment variable
 * @returns List of knowledge base documents
 * @throws Error if API request fails
 */
export async function getKnowledgeBaseList(apiKey?: string): Promise<any[]> {
  try {
    const client = createElevenLabsClient(apiKey);
    const documents = await client.conversationalAi.getKnowledgeBaseList();

    if (!Array.isArray(documents)) {
      console.warn("[ELEVENLABS] Unexpected response from getKnowledgeBaseList:", documents);
      return [];
    }

    return documents;
  } catch (error) {
    console.error("[ELEVENLABS] Error getting knowledge base list:", error);
    throw error;
  }
}

/**
 * Retrieves a knowledge base document by ID
 * 
 * @param docId - ID of the document to retrieve
 * @param apiKey - Optional API key to override the environment variable
 * @returns Knowledge base document details
 * @throws Error if document doesn't exist or API request fails
 */
export async function getKnowledgeBaseDocument(docId: string, apiKey?: string): Promise<any> {
  if (!docId || typeof docId !== 'string') {
    throw new Error("Document ID is required and must be a string");
  }

  try {
    const client = createElevenLabsClient(apiKey);
    const document = await client.conversationalAi.getKnowledgeBaseDocumentById(docId);

    if (!document || !document.id) {
      throw new Error(`Document with ID ${docId} not found or has invalid format`);
    }

    return document;
  } catch (error) {
    console.error(`[ELEVENLABS] Error getting knowledge base document ${docId}:`, error);
    throw error;
  }
}