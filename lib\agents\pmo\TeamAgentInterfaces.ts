/**
 * Team Agent Interfaces
 * 
 * This file contains interfaces for the five Agentic Teams:
 * - Marketing (Ag001)
 * - Research (Ag002)
 * - Software Design (Ag003)
 * - Sales (Ag004)
 * - Business Analysis (Ag005)
 * - Investigative Research (Ag006)
 */

import { AgenticTeamId } from './PMOInterfaces';
import { Task } from 'admin/planner/types';

// Base Team Agent Options
export interface TeamAgentOptions {
  userId: string;
  includeExplanation?: boolean;
  streamResponse?: boolean;
  onStreamUpdate?: (update: TeamAgentStreamUpdate) => void;
}

// Team Agent Stream Update
export interface TeamAgentStreamUpdate {
  stage: 'analyzing-task' | 'processing-task' | 'generating-output' | 'complete';
  data?: any;
  message?: string;
}

// Team Agent Result
export interface TeamAgentResult {
  success: boolean;
  taskId: string;
  output: string;
  outputDocumentIds: string[];
  error?: string;
}

// Base Team Agent Interface
export interface TeamAgent {
  processTask(task: Task): Promise<TeamAgentResult>;
}

// Marketing Team Agent Options
export interface MarketingTeamAgentOptions extends TeamAgentOptions {
  marketingSpecificOption?: string;
}

// Research Team Agent Options
export interface ResearchTeamAgentOptions extends TeamAgentOptions {
  researchSpecificOption?: string;
}

// Software Design Team Agent Options
export interface SoftwareDesignTeamAgentOptions extends TeamAgentOptions {
  softwareSpecificOption?: string;
}

// Sales Team Agent Options
export interface SalesTeamAgentOptions extends TeamAgentOptions {
  salesSpecificOption?: string;
}

// Business Analysis Team Agent Options
export interface BusinessAnalysisTeamAgentOptions extends TeamAgentOptions {
  businessAnalysisSpecificOption?: string;
}

// Investigative Research Team Agent Options
export interface InvestigativeResearchTeamAgentOptions extends TeamAgentOptions {
  investigationType?: string;
  selectedJournalistIds?: string[];
  consolidateReport?: boolean;
  criteriaModel?: string;
  optimizationModel?: string;
  assessmentModel?: string;
  consolidationModel?: string;
}

// Marketing Team Agent Result
export interface MarketingTeamAgentResult extends TeamAgentResult {
  marketingSpecificOutput?: any;
}

// Research Team Agent Result
export interface ResearchTeamAgentResult extends TeamAgentResult {
  researchSpecificOutput?: any;
}

// Software Design Team Agent Result
export interface SoftwareDesignTeamAgentResult extends TeamAgentResult {
  softwareSpecificOutput?: any;
}

// Sales Team Agent Result
export interface SalesTeamAgentResult extends TeamAgentResult {
  salesSpecificOutput?: any;
}

// Business Analysis Team Agent Result
export interface BusinessAnalysisTeamAgentResult extends TeamAgentResult {
  businessAnalysisSpecificOutput?: any;
}

// Investigative Research Team Agent Result
export interface InvestigativeResearchTeamAgentResult extends TeamAgentResult {
  investigationId: string;
  investigationType: string;
  journalistResponses: Array<{
    journalistId: string;
    journalistName: string;
    model: string;
    provider: string;
    response: string | null;
    error: string | null;
    investigationAngle: string;
    keyFindings: string[];
  }>;
  criteria: string;
  optimizedPrompt: string;
  assessment: string;
  consolidatedReport?: string;
  keyFindings: string[];
  recommendations: string[];
  sources: string[];
  modelConfiguration: {
    criteriaModel: string;
    optimizationModel: string;
    assessmentModel: string;
    consolidationModel?: string;
  };
}

// Team Factory Interface
export interface TeamAgentFactory {
  createTeamAgent(teamId: AgenticTeamId, options: TeamAgentOptions): TeamAgent;
}

// Team Agent Capabilities
export interface TeamAgentCapabilities {
  canGenerateImages: boolean;
  canGenerateDocuments: boolean;
  canGenerateCharts: boolean;
  canPerformResearch: boolean;
  canWriteCode: boolean;
  canAnalyzeData: boolean;
  canCreatePresentations: boolean;
}

// Marketing Team Capabilities
export const MARKETING_TEAM_CAPABILITIES: TeamAgentCapabilities = {
  canGenerateImages: true,
  canGenerateDocuments: true,
  canGenerateCharts: true,
  canPerformResearch: true,
  canWriteCode: false,
  canAnalyzeData: true,
  canCreatePresentations: true
};

// Research Team Capabilities
export const RESEARCH_TEAM_CAPABILITIES: TeamAgentCapabilities = {
  canGenerateImages: false,
  canGenerateDocuments: true,
  canGenerateCharts: true,
  canPerformResearch: true,
  canWriteCode: false,
  canAnalyzeData: true,
  canCreatePresentations: true
};

// Software Design Team Capabilities
export const SOFTWARE_DESIGN_TEAM_CAPABILITIES: TeamAgentCapabilities = {
  canGenerateImages: true,
  canGenerateDocuments: true,
  canGenerateCharts: true,
  canPerformResearch: false,
  canWriteCode: true,
  canAnalyzeData: true,
  canCreatePresentations: false
};

// Sales Team Capabilities
export const SALES_TEAM_CAPABILITIES: TeamAgentCapabilities = {
  canGenerateImages: false,
  canGenerateDocuments: true,
  canGenerateCharts: true,
  canPerformResearch: false,
  canWriteCode: false,
  canAnalyzeData: true,
  canCreatePresentations: true
};

// Business Analysis Team Capabilities
export const BUSINESS_ANALYSIS_TEAM_CAPABILITIES: TeamAgentCapabilities = {
  canGenerateImages: false,
  canGenerateDocuments: true,
  canGenerateCharts: true,
  canPerformResearch: true,
  canWriteCode: false,
  canAnalyzeData: true,
  canCreatePresentations: true
};

// Investigative Research Team Capabilities
export const INVESTIGATIVE_RESEARCH_TEAM_CAPABILITIES: TeamAgentCapabilities = {
  canGenerateImages: false,
  canGenerateDocuments: true,
  canGenerateCharts: false,
  canPerformResearch: true,
  canWriteCode: false,
  canAnalyzeData: true,
  canCreatePresentations: false
};

// Get team capabilities by ID
export function getTeamCapabilities(teamId: AgenticTeamId): TeamAgentCapabilities {
  switch (teamId) {
    case AgenticTeamId.Marketing:
      return MARKETING_TEAM_CAPABILITIES;
    case AgenticTeamId.Research:
      return RESEARCH_TEAM_CAPABILITIES;
    case AgenticTeamId.SoftwareDesign:
      return SOFTWARE_DESIGN_TEAM_CAPABILITIES;
    case AgenticTeamId.Sales:
      return SALES_TEAM_CAPABILITIES;
    case AgenticTeamId.BusinessAnalysis:
      return BUSINESS_ANALYSIS_TEAM_CAPABILITIES;
    case AgenticTeamId.InvestigativeResearch:
      return INVESTIGATIVE_RESEARCH_TEAM_CAPABILITIES;
    default:
      return RESEARCH_TEAM_CAPABILITIES;
  }
}

// Get team name by ID
export function getTeamName(teamId: AgenticTeamId): string {
  switch (teamId) {
    case AgenticTeamId.Marketing:
      return 'Marketing Team';
    case AgenticTeamId.Research:
      return 'Research Team';
    case AgenticTeamId.SoftwareDesign:
      return 'Software Design Team';
    case AgenticTeamId.Sales:
      return 'Sales Team';
    case AgenticTeamId.BusinessAnalysis:
      return 'Business Analysis Team';
    case AgenticTeamId.InvestigativeResearch:
      return 'Investigative Research Team';
    default:
      return 'Unknown Team';
  }
}

// Get team description by ID
export function getTeamDescription(teamId: AgenticTeamId): string {
  switch (teamId) {
    case AgenticTeamId.Marketing:
      return 'Specializes in marketing strategy, content creation, brand management, and market analysis.';
    case AgenticTeamId.Research:
      return 'Focuses on data collection, analysis, literature reviews, and producing research reports.';
    case AgenticTeamId.SoftwareDesign:
      return 'Handles software development, UI/UX design, coding, and technical implementation.';
    case AgenticTeamId.Sales:
      return 'Manages sales strategies, client relationships, proposal development, and revenue generation.';
    case AgenticTeamId.BusinessAnalysis:
      return 'Specializes in strategic planning, process analysis, requirements engineering, and software engineering documentation workflows.';
    case AgenticTeamId.InvestigativeResearch:
      return 'Conducts comprehensive investigative research using specialized journalist AI agents and multi-LLM analysis.';
    default:
      return 'Unknown team specialization.';
  }
}
