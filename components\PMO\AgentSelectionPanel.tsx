"use client";

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { But<PERSON> } from '../ui/button';
import {
  Users,
  Mic,
  Brain,
  TrendingUp,
  Code,
  DollarSign,
  BarChart3,
  Search,
  ChevronRight,
  Volume2,
  Loader2,
  CheckCircle2,
  XCircle,
  Pause,
  Info
} from 'lucide-react';
import { getPMOAgentConfig, PMOAgentVoiceConfig } from '../../lib/agents/voice/pmoAgentVoiceConfig';
import { PMOAgentInitializationService } from '../../lib/services/PMOAgentInitializationService';
import { generateVoicePreview } from '../scriptreaderAI/voiceUtils';

// --- PROPS INTERFACE ---
interface AgentSelectionPanelProps {
  onAgentSelect: (agentType: string) => void;
  availableAgents: string[];
  userId?: string;
  documentContext?: any[];
  selectedDocuments?: string[];
  onLoadDocuments?: (agentType: string) => Promise<void>;
}

// --- ICON MAPPING ---
const AGENT_ICONS: Record<string, React.ComponentType<any>> = {
  'Marketing': TrendingUp,
  'Research': Search,
  'SoftwareDesign': Code,
  'Sales': DollarSign,
  'BusinessAnalysis': BarChart3,
  'InvestigativeResearch': Brain
};


// --- AGENT CARD SUB-COMPONENT ---
const AgentCard = ({
    config,
    isSelected,
    isPlayingPreview,
    initStatus,
    onCardClick,
    onPreviewClick
}: {
    config: PMOAgentVoiceConfig;
    isSelected: boolean;
    isPlayingPreview: boolean;
    initStatus?: 'ready' | 'initializing' | 'error';
    onCardClick: () => void;
    onPreviewClick: (e: React.MouseEvent) => void;
}) => {
  const IconComponent = AGENT_ICONS[config.agentType] || Users;

  return (
    <div
      onClick={onCardClick}
      className={`
        relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300
        flex flex-col
        ${isSelected 
          ? 'border-purple-500 bg-purple-900/30 shadow-lg shadow-purple-900/20' 
          : 'border-gray-700 bg-gray-800/50 hover:border-gray-600 hover:bg-gray-800/80'
        }
      `}
    >
      <div className="flex items-center mb-4">
        <div className={`p-3 rounded-lg ${config.color || 'bg-gray-600'} mr-4`}>
          <IconComponent className="h-6 w-6 text-white" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-white">{config.agentName}</h3>
          <p className="text-sm text-gray-400">{config.agentId}</p>
        </div>
      </div>

      <p className="text-gray-300 text-sm mb-4 flex-grow">{config.description}</p>
      
      <div className="mb-5">
        <h4 className="text-xs font-medium text-gray-500 mb-2 uppercase tracking-wider">Specializations</h4>
        <div className="flex flex-wrap gap-1.5">
          {config.specialization.slice(0, 3).map((spec, index) => (
            <span key={index} className="px-2 py-1 bg-gray-700/80 text-gray-300 text-xs rounded">
              {spec}
            </span>
          ))}
          {config.specialization.length > 3 && (
            <span className="px-2 py-1 bg-gray-700/80 text-gray-400 text-xs rounded">
              +{config.specialization.length - 3} more
            </span>
          )}
        </div>
      </div>

      <Button
        variant="outline"
        size="sm"
        onClick={onPreviewClick}
        className="w-full text-gray-300 hover:text-white border-gray-600 hover:bg-gray-700/50 hover:border-purple-500"
      >
        {isPlayingPreview ? (
          <div className="flex items-center text-yellow-400">
            <Pause className="h-4 w-4 mr-2 animate-pulse" />
            Stop Preview
          </div>
        ) : (
          <>
            <Volume2 className="h-4 w-4 mr-2" />
            Voice Preview
          </>
        )}
      </Button>

      {/* Status Indicators */}
      <div className="absolute top-3 right-3 flex items-center justify-center h-6 w-6 rounded-full bg-gray-900/50">
        {initStatus === 'initializing' ? <Loader2 className="h-4 w-4 text-blue-400 animate-spin" />
        : initStatus === 'ready' ? <CheckCircle2 className="h-4 w-4 text-green-400" />
        : initStatus === 'error' ? <XCircle className="h-4 w-4 text-red-400" />
        : isSelected ? <CheckCircle2 className="h-4 w-4 text-purple-400" />
        : <div className="h-3 w-3 rounded-full border-2 border-gray-600"></div>}
      </div>
    </div>
  );
};


// --- MAIN AGENT SELECTION PANEL COMPONENT ---
export default function AgentSelectionPanel({
  onAgentSelect,
  availableAgents,
  userId,
}: AgentSelectionPanelProps) {
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [playingPreview, setPlayingPreview] = useState<string | null>(null);
  const [initializingAgent, setInitializingAgent] = useState<string | null>(null);
  const [agentInitStatus, setAgentInitStatus] = useState<Record<string, 'ready' | 'initializing' | 'error'>>({});
  const [voicePreviewError, setVoicePreviewError] = useState<string | null>(null);

  const audioRefs = useRef<Record<string, HTMLAudioElement>>({});

  useEffect(() => {
    if (availableAgents.length > 0 && !selectedAgent) {
      const defaultAgent = availableAgents.includes('Marketing') ? 'Marketing' : availableAgents[0];
      setSelectedAgent(defaultAgent);
    }
  }, [availableAgents, selectedAgent]);

  useEffect(() => {
    return () => {
      Object.values(audioRefs.current).forEach(audio => {
        if (audio) {
          audio.pause();
          if (audio.src.startsWith('blob:')) {
            URL.revokeObjectURL(audio.src);
          }
        }
      });
      audioRefs.current = {};
    };
  }, []);

  const handleAgentClick = useCallback((agentType: string) => {
    setSelectedAgent(agentType);
    if (voicePreviewError) setVoicePreviewError(null);
  }, [voicePreviewError]);

  const stopVoicePreview = useCallback(() => {
    if (playingPreview && audioRefs.current[playingPreview]) {
      audioRefs.current[playingPreview].pause();
      audioRefs.current[playingPreview].currentTime = 0;
      setPlayingPreview(null);
    }
  }, [playingPreview]);
  
  const playVoicePreview = useCallback(async (agentType: string) => {
    stopVoicePreview(); // Stop any other preview
    setPlayingPreview(agentType);
    setVoicePreviewError(null);

    const config = getPMOAgentConfig(agentType);
    if (!config) {
      setVoicePreviewError("Agent configuration not found.");
      setPlayingPreview(null);
      return;
    }

    try {
      const previewText = `Hello, I am ${config.agentName}. As a ${config.agentType} expert, I'm ready to assist you.`;
      const audioUrl = await generateVoicePreview(config.voiceId, previewText);

      const audio = new Audio(audioUrl);
      audioRefs.current[agentType] = audio;

      audio.onended = () => {
        setPlayingPreview(null);
        URL.revokeObjectURL(audioUrl);
      };
      audio.onerror = () => {
        setVoicePreviewError('Audio format not supported or file is invalid.');
        setPlayingPreview(null);
        URL.revokeObjectURL(audioUrl);
      };
      
      await audio.play();

    } catch (error) {
      console.error('[AGENT_SELECTION] Error generating voice preview:', error);
      let message = "Failed to generate voice preview.";
      if (error instanceof Error && error.message.includes('401')) {
          message = "Authentication failed with voice service.";
      }
      setVoicePreviewError(message);
      setPlayingPreview(null);
    }
  }, [stopVoicePreview]);

  const handlePreviewClick = useCallback((e: React.MouseEvent, agentType: string) => {
    e.stopPropagation();
    if (playingPreview === agentType) {
      stopVoicePreview();
    } else {
      playVoicePreview(agentType);
    }
  }, [playingPreview, stopVoicePreview, playVoicePreview]);
  
  const confirmSelection = useCallback(async () => {
    if (!selectedAgent || !userId) return;

    const agentConfig = getPMOAgentConfig(selectedAgent);
    if (!agentConfig) return;

    setInitializingAgent(selectedAgent);
    setAgentInitStatus(prev => ({ ...prev, [selectedAgent]: 'initializing' }));

    try {
      // NOTE: This uses a mock service initialization. Replace with your actual implementation.
      const initService = PMOAgentInitializationService.getInstance();
      const result = await initService.initializeAgent({
        userId,
        agentConfig,
        agentId: `${userId}-pmo-${selectedAgent.toLowerCase()}`,
        documentContext: [] // Simplified for example
      });

      if (result.success) {
        setAgentInitStatus(prev => ({ ...prev, [selectedAgent]: 'ready' }));
        setTimeout(() => onAgentSelect(selectedAgent), 500); // Small delay for user to see 'ready' state
      } else {
        throw new Error(result.error || "Unknown initialization failure");
      }
    } catch (error) {
      console.error(`[AGENT_SELECTION] Error during agent initialization:`, error);
      setAgentInitStatus(prev => ({ ...prev, [selectedAgent]: 'error' }));
    } finally {
      setInitializingAgent(null);
    }
  }, [selectedAgent, userId, onAgentSelect]);
  
  const getButtonState = () => {
    if(!selectedAgent) return { text: "Select an Agent", disabled: true };
    const config = getPMOAgentConfig(selectedAgent);
    if (!config) return { text: "Invalid Agent", disabled: true };

    if (initializingAgent === selectedAgent) {
        return { text: `Initializing ${config.agentName}...`, Icon: Loader2, disabled: true };
    }
    if (agentInitStatus[selectedAgent] === 'ready') {
        return { text: `Start Meeting`, Icon: CheckCircle2, disabled: false };
    }
    if (agentInitStatus[selectedAgent] === 'error') {
        return { text: 'Retry Initialization', Icon: XCircle, disabled: false };
    }
    return { text: `Initialize & Start Meeting`, Icon: ChevronRight, disabled: false };
  }

  const { text: buttonText, Icon: ButtonIcon, disabled: buttonDisabled } = getButtonState();

  return (
    <div className="h-full w-full flex flex-col items-center justify-center p-6 bg-gray-900 text-white overflow-y-auto">
      <div className="w-full max-w-5xl mx-auto">
        <header className="text-center mb-10">
          <h1 className="text-4xl font-bold tracking-tight text-white">Select Your PMO Agent</h1>
          <p className="mt-3 text-lg text-gray-400">Choose an expert to begin your voice-enabled strategy session.</p>
        </header>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {availableAgents.map((agentType) => {
            const config = getPMOAgentConfig(agentType);
            if (!config) return null;

            return (
              <AgentCard
                key={agentType}
                config={config}
                isSelected={selectedAgent === agentType}
                isPlayingPreview={playingPreview === agentType}
                initStatus={agentInitStatus[agentType]}
                onCardClick={() => handleAgentClick(agentType)}
                onPreviewClick={(e) => handlePreviewClick(e, agentType)}
              />
            );
          })}
        </div>

        {voicePreviewError && (
            <div className="flex items-center justify-center p-3 mb-6 bg-red-900/50 border border-red-500/50 rounded-lg text-red-300 text-sm">
                <Info className="h-5 w-5 mr-3 flex-shrink-0" />
                {voicePreviewError}
            </div>
        )}

        <footer className="flex justify-center">
          <Button
            size="lg"
            onClick={confirmSelection}
            disabled={buttonDisabled || !userId}
            className="px-8 py-6 text-base font-semibold bg-purple-600 hover:bg-purple-700 text-white rounded-lg shadow-lg transition-transform transform hover:scale-105"
          >
            {ButtonIcon && <ButtonIcon className={`h-5 w-5 mr-2 ${initializingAgent ? 'animate-spin' : ''}`} />}
            {buttonText}
          </Button>
        </footer>
      </div>
    </div>
  );
}