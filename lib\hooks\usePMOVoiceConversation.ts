"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import { useConversation } from '@elevenlabs/react';
import { PMOAgentVoiceConfig, generatePMOAgentPrompt } from '../agents/voice/pmoAgentVoiceConfig';

export interface VoiceConversationState {
  isConnected: boolean;
  isConnecting: boolean;
  isSpeaking: boolean;
  isListening: boolean;
  isMuted: boolean;
  volume: number;
  error: string | null;
  conversationId: string | null;
  agentId: string | null;
  connectionStatus: string | null; // Detailed status message
  progress: number; // Progress percentage (0-100)
}

export interface VoiceConversationCallbacks {
  onConnect: () => void;
  onDisconnect: () => void;
  onError: (error: string) => void;
  onAgentSpeaking: (speaking: boolean) => void;
  onUserSpeaking: (speaking: boolean) => void;
  onMessage: (message: string, isUser: boolean) => void;
}

interface DocumentContext {
  documents: any[];
  selectedDocuments: string[];
  queuedDocuments: string[];
  isLoading: boolean;
}

interface UsePMOVoiceConversationProps {
  agentConfig: PMOAgentVoiceConfig | null;
  agentId: string | null;
  documentContext?: DocumentContext;
  callbacks?: Partial<VoiceConversationCallbacks>;
}

/**
 * Custom hook for managing PMO Agent voice conversations using ElevenLabs
 */
export function usePMOVoiceConversation({
  agentConfig,
  agentId,
  documentContext = { documents: [], selectedDocuments: [], queuedDocuments: [], isLoading: false },
  callbacks = {}
}: UsePMOVoiceConversationProps) {
  
  // State management
  const [state, setState] = useState<VoiceConversationState>({
    isConnected: false,
    isConnecting: false,
    isSpeaking: false,
    isListening: false,
    isMuted: false,
    volume: 0.8,
    error: null,
    conversationId: null,
    agentId: null,
    connectionStatus: null,
    progress: 0
  });

  // Refs for managing conversation state
  const agentCreatedRef = useRef<boolean>(false);

  // State to track the actual ElevenLabs agent ID
  const [elevenLabsAgentId, setElevenLabsAgentId] = useState<string | null>(null);

  // ElevenLabs conversation hook
  const conversation = useConversation({
    apiKey: process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY,
    agentId: elevenLabsAgentId ?? undefined, // Use the actual ElevenLabs agent ID
    maxReconnectAttempts: 3,
    reconnectInterval: 2000,
    // Add audio configuration to prevent invalid audio data
    ...(typeof window !== 'undefined' && {
      audioConfig: {
        sampleRate: 16000,
        channels: 1,
        bitDepth: 16
      }
    }),
    onConnect: () => {
      console.log('[PMO_VOICE] Connected to ElevenLabs');
      console.log('[PMO_VOICE] Connected with agent ID:', elevenLabsAgentId);
      console.log('[PMO_VOICE] Conversation object:', conversation);
      console.log('[PMO_VOICE] Conversation status:', conversation?.status);

      // Try to ensure audio output is enabled
      if (conversation) {
        const conv = conversation as any;
        console.log('[PMO_VOICE] Conversation properties:', Object.keys(conv));

        // Check for audio-related properties
        if (conv.audioElement) {
          console.log('[PMO_VOICE] Found audioElement:', conv.audioElement);
          conv.audioElement.volume = 1.0;
          conv.audioElement.muted = false;
        }

        if (conv.setVolume) {
          console.log('[PMO_VOICE] Setting volume via conversation.setVolume');
          conv.setVolume(1.0);
        }
      }

      setState(prev => ({
        ...prev,
        isConnected: true,
        isConnecting: false,
        error: null
      }));
      callbacks.onConnect?.();
    },
    onDisconnect: () => {
      console.log('[PMO_VOICE] Disconnected from ElevenLabs');
      setState(prev => ({ 
        ...prev, 
        isConnected: false, 
        isConnecting: false 
      }));
      callbacks.onDisconnect?.();
    },
    onError: (error: unknown) => {
      console.error('[PMO_VOICE] ElevenLabs error:', error);
      let errorMessage = error instanceof Error ? error.message : 'Voice connection error';

      // Handle specific audio-related errors
      if (errorMessage.includes('non-finite') || errorMessage.includes('audio') || errorMessage.includes('float')) {
        errorMessage = 'Audio processing error. Please check your microphone and try again.';
        console.error('[PMO_VOICE] Audio data error detected. This may be due to microphone issues or browser compatibility.');
      }

      setState(prev => ({
        ...prev,
        error: errorMessage,
        isConnecting: false
      }));
      callbacks.onError?.(errorMessage);
    },
    onModeChange: (mode: { mode: string; }) => {
      console.log('[PMO_VOICE] Mode changed:', mode);
      console.log('[PMO_VOICE] Mode details:', {
        mode: mode.mode,
        timestamp: new Date().toISOString(),
        conversationStatus: conversation?.status,
        audioContext: typeof AudioContext !== 'undefined' ? 'available' : 'not available',
        userAgent: navigator.userAgent
      });

      setState(prev => ({
        ...prev,
        isListening: mode.mode === 'listening',
        isSpeaking: mode.mode === 'speaking'
      }));

      if (mode.mode === 'speaking') {
        console.log('[PMO_VOICE] Agent started speaking - checking audio output');
        callbacks.onAgentSpeaking?.(true);
      } else {
        console.log('[PMO_VOICE] Agent stopped speaking');
        callbacks.onAgentSpeaking?.(false);
      }
    },
    onMessage: (message) => {
      console.log('[PMO_VOICE] Message received:', message);
      callbacks.onMessage?.(message.message, message.source === 'user');
    }
  });

  /**
   * Create or get ElevenLabs agent for the PMO agent type
   */
  const createOrGetAgent = useCallback(async () => {
    console.log('[PMO_VOICE] createOrGetAgent called with:', {
      agentConfig: !!agentConfig,
      agentId,
      agentCreatedRef: agentCreatedRef.current
    });

    if (!agentConfig) {
      console.log('[PMO_VOICE] No agentConfig provided');
      return null;
    }

    if (!agentId) {
      console.log('[PMO_VOICE] No agentId provided');
      return null;
    }

    if (agentCreatedRef.current) {
      console.log('[PMO_VOICE] Agent already created');
      return null;
    }

    try {
      console.log(`[PMO_VOICE] Creating/getting agent for ${agentConfig.agentType}`);

      // Update status: Starting agent creation
      setState(prev => ({
        ...prev,
        connectionStatus: 'Preparing agent configuration...',
        progress: 10
      }));

      // Filter to only selected documents
      const selectedDocuments = documentContext.documents.filter((doc: any) =>
        documentContext.selectedDocuments?.includes(doc.id)
      );

      console.log(`[PMO_VOICE] Document selection:`, {
        totalDocuments: documentContext.documents.length,
        selectedDocuments: selectedDocuments.length,
        selectedIds: documentContext.selectedDocuments
      });

      // Update status: Processing documents
      setState(prev => ({
        ...prev,
        connectionStatus: selectedDocuments.length > 0
          ? `Processing ${selectedDocuments.length} selected document${selectedDocuments.length !== 1 ? 's' : ''}...`
          : 'Creating agent without documents...',
        progress: 25
      }));

      // Generate agent prompt with context
      const prompt = generatePMOAgentPrompt(agentConfig, {
        documentCount: selectedDocuments.length,
        projectCount: selectedDocuments.filter((doc: any) => doc.projectId).length,
        recentProjects: selectedDocuments
          .filter((doc: any) => doc.projectId)
          .slice(0, 3)
          .map((doc: any) => doc.title)
      });

      // Update status: Creating agent
      setState(prev => ({
        ...prev,
        connectionStatus: 'Creating ElevenLabs agent...',
        progress: 50
      }));

      // Create agent using ElevenLabs API
      const response = await fetch('/api/elevenlabs/create-agent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agentId,
          name: agentConfig.agentName,
          voiceId: agentConfig.voiceId,
          prompt,
          knowledgeBase: selectedDocuments.map((doc: any) => ({
            id: doc.id,
            title: doc.title,
            content: doc.content,
            metadata: doc.metadata
          }))
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[PMO_VOICE] Agent creation failed:', {
          status: response.status,
          statusText: response.statusText,
          errorText
        });
        throw new Error(`Failed to create ElevenLabs agent: ${response.status} ${response.statusText} - ${errorText}`);
      }

      // Update status: Processing response
      setState(prev => ({
        ...prev,
        connectionStatus: 'Finalizing agent setup...',
        progress: 75
      }));

      const result = await response.json();
      console.log('[PMO_VOICE] Agent creation response:', result);
      agentCreatedRef.current = true;

      // Set the actual ElevenLabs agent ID for the conversation hook
      setElevenLabsAgentId(result.agentId);

      // Update status: Agent ready
      setState(prev => ({
        ...prev,
        agentId: result.agentId,
        conversationId: result.conversationId,
        connectionStatus: 'Agent ready for conversation!',
        progress: 100
      }));

      console.log(`[PMO_VOICE] Agent created successfully: ${result.agentId}`);
      console.log(`[PMO_VOICE] Setting ElevenLabs agent ID for conversation: ${result.agentId}`);

      // Clear status after a short delay
      setTimeout(() => {
        setState(prev => ({
          ...prev,
          connectionStatus: null,
          progress: 0
        }));
      }, 2000);

      return result;

    } catch (error) {
      console.error('[PMO_VOICE] Error creating agent:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create agent';
      setState(prev => ({ ...prev, error: errorMessage }));
      return null;
    }
  }, [agentConfig, agentId, documentContext, callbacks]);

  /**
   * Check audio permissions and devices
   */
  const checkAudioSetup = async () => {
    try {
      console.log('[PMO_VOICE] Checking audio setup...');

      // Check if audio context is available
      const audioContextAvailable = typeof AudioContext !== 'undefined' || typeof (window as any).webkitAudioContext !== 'undefined';
      console.log('[PMO_VOICE] AudioContext available:', audioContextAvailable);

      // Check media devices
      if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const audioOutputs = devices.filter(device => device.kind === 'audiooutput');
        const audioInputs = devices.filter(device => device.kind === 'audioinput');

        console.log('[PMO_VOICE] Audio devices:', {
          outputs: audioOutputs.length,
          inputs: audioInputs.length,
          outputDevices: audioOutputs.map(d => ({ deviceId: d.deviceId, label: d.label })),
          inputDevices: audioInputs.map(d => ({ deviceId: d.deviceId, label: d.label }))
        });
      }

      // Check microphone permissions with specific constraints
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
            sampleRate: 16000,
            channelCount: 1
          }
        });
        console.log('[PMO_VOICE] Microphone access granted');

        // Test for valid audio data to prevent non-finite errors
        const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
        if (AudioContextClass) {
          const audioContext = new AudioContextClass();
          const source = audioContext.createMediaStreamSource(stream);
          const analyser = audioContext.createAnalyser();
          source.connect(analyser);

          // Quick validation test
          const dataArray = new Float32Array(analyser.frequencyBinCount);
          analyser.getFloatFrequencyData(dataArray);

          const hasInvalidData = dataArray.some(value => !isFinite(value));
          if (hasInvalidData) {
            console.warn('[PMO_VOICE] Invalid audio data detected in microphone stream');
          }

          audioContext.close();
        }

        stream.getTracks().forEach(track => track.stop()); // Clean up
      } catch (micError) {
        console.warn('[PMO_VOICE] Microphone access denied or unavailable:', micError);
      }

    } catch (error) {
      console.error('[PMO_VOICE] Error checking audio setup:', error);
    }
  };

  /**
   * Start voice conversation
   */
  const startConversation = useCallback(async () => {
    if (!agentConfig || state.isConnecting || state.isConnected) {
      return;
    }

    setState(prev => ({
      ...prev,
      isConnecting: true,
      error: null,
      connectionStatus: 'Checking audio setup...',
      progress: 5
    }));

    // Check audio setup before starting
    await checkAudioSetup();

    try {
      console.log('[PMO_VOICE] Starting conversation...');
      console.log('[PMO_VOICE] Current state:', {
        agentConfig: !!agentConfig,
        agentId,
        elevenLabsAgentId,
        agentCreatedRef: agentCreatedRef.current
      });

      // Create or get agent first
      const agent = await createOrGetAgent();
      console.log('[PMO_VOICE] createOrGetAgent result:', agent);

      if (!agent) {
        const errorDetails = [];
        if (!agentConfig) errorDetails.push('No agent configuration');
        if (!agentId) errorDetails.push('No agent ID');
        if (agentCreatedRef.current) errorDetails.push('Agent already created');

        const errorMessage = `Failed to initialize agent: ${errorDetails.join(', ')}`;
        console.error('[PMO_VOICE]', errorMessage);
        throw new Error(errorMessage);
      }

      // Use the agent ID from the agent creation response directly
      const actualAgentId = agent.agentId;
      console.log('[PMO_VOICE] Using agent ID from creation response:', actualAgentId);

      // Update status: Starting conversation
      setState(prev => ({
        ...prev,
        connectionStatus: 'Connecting to voice conversation...',
        progress: 90
      }));

      // Start ElevenLabs conversation
      console.log('[PMO_VOICE] Starting conversation session with ElevenLabs agent:', actualAgentId);
      console.log('[PMO_VOICE] Conversation configuration before startSession:', {
        actualAgentId: actualAgentId,
        customAgentId: agent.agentId,
        conversationObject: conversation,
        conversationStatus: conversation?.status,
        apiKey: process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY ? 'present' : 'missing'
      });

      await conversation.startSession({
        agentId: actualAgentId, // Use the actual ElevenLabs agent ID from response
        // Add any additional session configuration
      });

      console.log('[PMO_VOICE] Session started, conversation status:', conversation?.status);

      // Update status: Connected
      setState(prev => ({
        ...prev,
        connectionStatus: 'Connected! You can now speak.',
        progress: 100
      }));

      // Clear status after a short delay
      setTimeout(() => {
        setState(prev => ({
          ...prev,
          connectionStatus: null,
          progress: 0
        }));
      }, 3000);

      // Debug conversation object properties
      const conversationAny = conversation as any;
      console.log('[PMO_VOICE] Conversation object properties:', {
        status: conversation?.status,
        agentId: conversation?.getId?.(),
        isConnected: conversationAny?.isConnected,
        isMuted: conversationAny?.isMuted,
        volume: conversationAny?.volume,
        methods: Object.getOwnPropertyNames(conversation || {}).filter(prop => typeof conversationAny?.[prop] === 'function'),
        properties: Object.keys(conversation || {})
      });

      console.log('[PMO_VOICE] Conversation started successfully');

    } catch (error) {
      console.error('[PMO_VOICE] Error starting conversation:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to start conversation';
      setState(prev => ({ 
        ...prev, 
        error: errorMessage, 
        isConnecting: false 
      }));
    }
  }, [agentConfig, state.isConnecting, state.isConnected, conversation, createOrGetAgent, elevenLabsAgentId]);

  /**
   * End voice conversation
   */
  const endConversation = useCallback(async () => {
    if (!state.isConnected) {
      return;
    }

    try {
      await conversation.endSession();
      agentCreatedRef.current = false;
      
      setState(prev => ({
        ...prev,
        isConnected: false,
        isConnecting: false,
        isSpeaking: false,
        isListening: false,
        conversationId: null,
        error: null
      }));

      console.log('[PMO_VOICE] Conversation ended successfully');

    } catch (error) {
      console.error('[PMO_VOICE] Error ending conversation:', error);
    }
  }, [state.isConnected, conversation]);

  /**
   * Toggle mute
   */
  const toggleMute = useCallback(() => {
    const newMutedState = !state.isMuted;
    setState(prev => ({ ...prev, isMuted: newMutedState }));
    
    // Apply mute to ElevenLabs conversation
    if (conversation && state.isConnected) {
      // TODO: Implement mute functionality with ElevenLabs
      console.log(`[PMO_VOICE] Mute toggled: ${newMutedState}`);
    }
  }, [state.isMuted, state.isConnected, conversation]);

  /**
   * Try to manually control audio output
   */
  const debugAudioOutput = useCallback(() => {
    console.log('[PMO_VOICE] Debugging audio output...');

    // Try to find audio elements in the DOM
    const audioElements = document.querySelectorAll('audio');
    console.log('[PMO_VOICE] Found audio elements:', audioElements.length);

    audioElements.forEach((audio, index) => {
      console.log(`[PMO_VOICE] Audio element ${index}:`, {
        src: audio.src,
        volume: audio.volume,
        muted: audio.muted,
        paused: audio.paused,
        currentTime: audio.currentTime,
        duration: audio.duration,
        readyState: audio.readyState
      });

      // Try to increase volume and unmute
      audio.volume = 1.0;
      audio.muted = false;

      // Try to play if paused
      if (audio.paused) {
        audio.play().catch(e => console.log('[PMO_VOICE] Could not play audio element:', e));
      }
    });

    // Check if conversation object has audio-related properties
    if (conversation) {
      console.log('[PMO_VOICE] Checking conversation for audio properties...');
      const conversationObj = conversation as any;

      // Try common audio property names
      const audioProps = ['audioElement', 'audio', 'audioContext', 'audioNode', 'mediaElement', 'player'];
      audioProps.forEach(prop => {
        if (conversationObj[prop]) {
          console.log(`[PMO_VOICE] Found ${prop}:`, conversationObj[prop]);

          // If it's an audio element, try to control it
          if (conversationObj[prop] instanceof HTMLAudioElement) {
            const audioEl = conversationObj[prop] as HTMLAudioElement;
            audioEl.volume = 1.0;
            audioEl.muted = false;
            console.log(`[PMO_VOICE] Set volume and unmuted ${prop}`);
          }
        }
      });
    }
  }, [conversation]);

  /**
   * Adjust volume
   */
  const setVolume = useCallback((volume: number) => {
    const clampedVolume = Math.max(0, Math.min(1, volume));
    setState(prev => ({ ...prev, volume: clampedVolume }));

    // Apply volume to ElevenLabs conversation
    if (conversation && state.isConnected) {
      console.log(`[PMO_VOICE] Volume set to: ${clampedVolume}`);

      // Try to apply volume to any audio elements
      debugAudioOutput();

      // Try to set volume on conversation object if it has volume property
      const conversationObj = conversation as any;
      if (typeof conversationObj.setVolume === 'function') {
        conversationObj.setVolume(clampedVolume);
        console.log(`[PMO_VOICE] Applied volume via conversation.setVolume()`);
      }
    }
  }, [state.isConnected, conversation, debugAudioOutput]);

  /**
   * Send text message to agent
   * Note: ElevenLabs conversation is primarily voice-based
   */
  const sendMessage = useCallback(async (message: string) => {
    if (!state.isConnected || !conversation) {
      return;
    }

    try {
      // Cast to any since sendMessage might not be available in all versions
      const conversationAny = conversation as any;
      if (conversationAny.sendMessage) {
        await conversationAny.sendMessage(message);
        console.log(`[PMO_VOICE] Message sent: ${message}`);
      } else {
        console.warn('[PMO_VOICE] sendMessage not available - conversation is voice-only');
      }
    } catch (error) {
      console.error('[PMO_VOICE] Error sending message:', error);
    }
  }, [state.isConnected, conversation]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (state.isConnected) {
        endConversation();
      }
    };
  }, []);

  // Reset agent creation flag when agent config changes
  useEffect(() => {
    agentCreatedRef.current = false;
  }, [agentConfig?.agentType]);

  return {
    state,
    conversation,
    startConversation,
    endConversation,
    toggleMute,
    setVolume,
    sendMessage,
    debugAudioOutput,
    isReady: !!agentConfig && !!agentId
  };
}
